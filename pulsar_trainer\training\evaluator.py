"""
Evaluation utilities for GFNet pulsar classification.

This module provides evaluation functionality including metrics calculation,
performance target checking, and result analysis.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any, Tuple, Optional
from abc import ABC, abstractmethod
import logging
from pathlib import Path

# Import existing evaluation functions
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.join(os.path.dirname(__file__), '../../pulsar-identification-main'))

try:
    from evaluation.metrics import extended_outputanalysis
    EXTENDED_ANALYSIS_AVAILABLE = True
except ImportError:
    EXTENDED_ANALYSIS_AVAILABLE = False
    # Use logging instead of warnings to avoid repetitive output
    import logging
    logger = logging.getLogger(__name__)
    logger.info("Extended analysis module not available, using basic metrics only")

# Import comprehensive misclassification analyzer with improved error handling
try:
    from ..utils.comprehensive_misclassification_analysis import ComprehensiveMisclassificationAnalyzer
except ImportError:
    # Fallback for direct execution or different project structures
    import sys
    from pathlib import Path

    # Add project root to sys.path
    project_root = Path(__file__).parent.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    try:
        from pulsar_trainer.utils.comprehensive_misclassification_analysis import ComprehensiveMisclassificationAnalyzer
    except ImportError:
        # Final fallback
        sys.path.append(str(Path(__file__).parent.parent))
        from utils.comprehensive_misclassification_analysis import ComprehensiveMisclassificationAnalyzer


class BaseEvaluator(ABC):
    """Abstract base class for evaluators."""

    @abstractmethod
    def evaluate(self, model: nn.Module, dataloader, device: torch.device) -> Dict[str, Any]:
        """Evaluate model performance."""
        pass


class PulsarEvaluator(BaseEvaluator):
    """
    Evaluator for pulsar classification models.

    Provides comprehensive evaluation including basic metrics,
    performance target checking, and detailed analysis.
    """

    def __init__(self, performance_targets: Dict[str, float]):
        """
        Initialize evaluator.

        Args:
            performance_targets: Dictionary of performance targets
        """
        self.performance_targets = performance_targets
        self.logger = logging.getLogger(__name__)

    def evaluate(self, model: nn.Module, dataloader, device: torch.device,
                 output_dir: Optional[Path] = None) -> Dict[str, Any]:
        """
        Evaluate model performance with comprehensive misclassification analysis.

        Args:
            model: Model to evaluate
            dataloader: Data loader for evaluation
            device: Device to run evaluation on
            output_dir: Output directory for comprehensive analysis (optional)

        Returns:
            Dictionary containing evaluation results and comprehensive analysis
        """
        model.eval()
        all_outputs = []
        all_targets = []
        total_loss = 0.0
        criterion = nn.CrossEntropyLoss()

        with torch.no_grad():
            for batch_idx, (data, target) in enumerate(dataloader):
                data, target = data.to(device), target.to(device)

                # Forward pass
                output = model(data)

                # Calculate loss
                loss = criterion(output, target)
                total_loss += loss.item()

                # Get probabilities
                probs = torch.softmax(output, dim=1)

                # Store results
                all_outputs.append(probs.cpu().numpy())
                all_targets.append(target.cpu().numpy())

        # Combine results
        outputs = np.vstack(all_outputs)
        targets = np.concatenate(all_targets)
        avg_loss = total_loss / len(dataloader)

        # Calculate metrics
        results = self._calculate_metrics(outputs, targets, avg_loss)

        # Check performance targets
        self._check_performance_targets(results)

        # Add extended analysis if available
        if EXTENDED_ANALYSIS_AVAILABLE:
            try:
                extended_results = extended_outputanalysis(outputs, targets, save_plots=True)
                results['extended_analysis'] = extended_results
            except Exception as e:
                self.logger.warning(f"Extended analysis failed: {e}")

        # Add comprehensive misclassification analysis if output directory is provided
        if output_dir is not None:
            self.logger.info("开始全面误分类分析...")

            # Determine modality from dataloader
            modality = self._determine_modality(dataloader)

            # Create comprehensive misclassification analyzer
            comprehensive_analyzer = ComprehensiveMisclassificationAnalyzer(
                output_dir=output_dir,
                modality=modality
            )

            # Execute comprehensive analysis
            misclassification_results = comprehensive_analyzer.analyze_all_misclassifications(
                dataset=dataloader.dataset,
                predictions=results['predictions'],
                targets=results['targets'],
                probabilities=results['probabilities']
            )

            # Add misclassification analysis results to the main results
            results['comprehensive_misclassification_analysis'] = misclassification_results

            self.logger.info("全面误分类分析完成")

        return results

    def _calculate_metrics(self, outputs: np.ndarray, targets: np.ndarray, avg_loss: float) -> Dict[str, Any]:
        """
        Calculate basic evaluation metrics.

        Args:
            outputs: Model outputs (probabilities)
            targets: True labels
            avg_loss: Average loss

        Returns:
            Dictionary of metrics
        """
        # Get predictions
        predictions = np.argmax(outputs, axis=1)

        # Basic metrics
        accuracy = np.mean(predictions == targets)

        # Class-specific metrics
        tp = np.sum((predictions == 1) & (targets == 1))  # True positives
        tn = np.sum((predictions == 0) & (targets == 0))  # True negatives
        fp = np.sum((predictions == 1) & (targets == 0))  # False positives
        fn = np.sum((predictions == 0) & (targets == 1))  # False negatives

        # Calculate metrics with division by zero protection
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0.0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

        # False positive rate
        false_positive_rate = fp / (fp + tn) if (fp + tn) > 0 else 0.0

        # Confusion matrix
        confusion_matrix = {
            'tp': int(tp),
            'tn': int(tn),
            'fp': int(fp),
            'fn': int(fn)
        }

        # Class distribution
        class_distribution = {
            'positive_samples': int(np.sum(targets == 1)),
            'negative_samples': int(np.sum(targets == 0)),
            'total_samples': len(targets)
        }

        # Prepare basic results
        results = {
            'loss': avg_loss,
            'basic_metrics': {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'specificity': specificity,
                'f1_score': f1_score,
                'false_positive_rate': false_positive_rate
            },
            'confusion_matrix': confusion_matrix,
            'class_distribution': class_distribution,
            'predictions': predictions,
            'probabilities': outputs,
            'targets': targets
        }

        return results

    def _determine_modality(self, dataloader) -> str:
        """
        确定数据模态。

        Args:
            dataloader: 数据加载器

        Returns:
            数据模态字符串 ('FPP', 'TPP', 或 'Unknown')
        """
        if hasattr(dataloader, 'dataset') and hasattr(dataloader.dataset, 'modality'):
            return dataloader.dataset.modality
        elif hasattr(dataloader, 'dataset') and hasattr(dataloader.dataset, 'data_dir'):
            # Try to infer modality from data directory path
            data_dir_str = str(dataloader.dataset.data_dir)
            if 'FPP' in data_dir_str:
                return 'FPP'
            elif 'TPP' in data_dir_str:
                return 'TPP'
        return 'Unknown'

    def _check_performance_targets(self, results: Dict[str, Any]) -> None:
        """
        Check if performance targets are met (internal tracking only).

        Args:
            results: Results dictionary to update
        """
        metrics = results['basic_metrics']
        meets_targets = True
        target_results = {}

        for metric, target in self.performance_targets.items():
            if metric in metrics:
                actual = metrics[metric]

                if metric == 'false_positive_rate':
                    meets = actual <= target  # FPR should be lower
                else:
                    meets = actual >= target  # Other metrics should be higher

                target_results[metric] = {
                    'target': target,
                    'actual': actual,
                    'meets_target': meets
                }

                if not meets:
                    meets_targets = False

        # Store results for internal use but don't display status messages
        results['performance_targets'] = target_results
        results['meets_performance_targets'] = meets_targets

    def save_results(self, results: Dict[str, Any], output_dir: str) -> None:
        """
        Save evaluation results to files.

        Args:
            results: Results dictionary
            output_dir: Output directory
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Save simplified metrics (without performance target comparisons)
        metrics_file = output_dir / 'evaluation_metrics.txt'
        with open(metrics_file, 'w', encoding='utf-8') as f:
            f.write("=== Pulsar Classification Evaluation Results ===\n\n")

            # Basic metrics
            f.write("Performance Metrics:\n")
            for metric, value in results['basic_metrics'].items():
                f.write(f"  {metric}: {value:.4f}\n")

            f.write("\nConfusion Matrix:\n")
            cm = results['confusion_matrix']
            f.write(f"  True Positives:  {cm['tp']}\n")
            f.write(f"  True Negatives:  {cm['tn']}\n")
            f.write(f"  False Positives: {cm['fp']}\n")
            f.write(f"  False Negatives: {cm['fn']}\n")

            # Class distribution
            f.write("\nClass Distribution:\n")
            dist = results['class_distribution']
            f.write(f"  Positive Samples: {dist['positive_samples']}\n")
            f.write(f"  Negative Samples: {dist['negative_samples']}\n")
            f.write(f"  Total Samples: {dist['total_samples']}\n")

            # Additional derived metrics
            total_samples = dist['total_samples']
            accuracy = results['basic_metrics']['accuracy']
            f.write(f"\nAdditional Information:\n")
            f.write(f"  Correctly Classified: {int(accuracy * total_samples)}\n")
            f.write(f"  Misclassified: {int((1 - accuracy) * total_samples)}\n")

        # Save predictions and probabilities (useful for further analysis)
        np.save(output_dir / 'predictions.npy', results['predictions'])
        np.save(output_dir / 'probabilities.npy', results['probabilities'])
        np.save(output_dir / 'targets.npy', results['targets'])

        self.logger.info(f"评估结果已保存到: {output_dir}")

    def compare_models(self, results_list: list, model_names: list) -> Dict[str, Any]:
        """
        Compare multiple model evaluation results.

        Args:
            results_list: List of evaluation results
            model_names: List of model names

        Returns:
            Comparison results
        """
        if len(results_list) != len(model_names):
            raise ValueError("Number of results and model names must match")

        comparison = {
            'models': model_names,
            'metrics_comparison': {},
            'best_model': {}
        }

        # Compare each metric
        metrics_to_compare = ['accuracy', 'precision', 'recall', 'f1_score', 'false_positive_rate']

        for metric in metrics_to_compare:
            values = []
            for results in results_list:
                if metric in results['basic_metrics']:
                    values.append(results['basic_metrics'][metric])
                else:
                    values.append(None)

            comparison['metrics_comparison'][metric] = {
                'values': values,
                'best_index': self._get_best_index(values, metric),
                'best_value': max(values) if metric != 'false_positive_rate' else min(values)
            }

        # Determine overall best model (based on F1 score)
        f1_values = comparison['metrics_comparison']['f1_score']['values']
        best_idx = np.argmax(f1_values)
        comparison['best_model'] = {
            'index': best_idx,
            'name': model_names[best_idx],
            'f1_score': f1_values[best_idx]
        }

        return comparison

    def _get_best_index(self, values: list, metric: str) -> int:
        """Get index of best value for a metric."""
        if metric == 'false_positive_rate':
            return np.argmin(values)  # Lower is better for FPR
        else:
            return np.argmax(values)  # Higher is better for other metrics


class EvaluatorFactory:
    """Factory for creating evaluators."""

    _evaluators = {
        'pulsar': PulsarEvaluator
    }

    @classmethod
    def register(cls, name: str, evaluator_class: type) -> None:
        """Register a new evaluator class."""
        cls._evaluators[name] = evaluator_class

    @classmethod
    def create(cls, name: str, **kwargs) -> BaseEvaluator:
        """Create an evaluator instance."""
        if name not in cls._evaluators:
            available = list(cls._evaluators.keys())
            raise ValueError(f"Unknown evaluator: {name}. Available: {available}")

        return cls._evaluators[name](**kwargs)

    @classmethod
    def get_available_evaluators(cls) -> list:
        """Get list of available evaluators."""
        return list(cls._evaluators.keys())


# Register default evaluators
EvaluatorFactory.register('pulsar', PulsarEvaluator)
