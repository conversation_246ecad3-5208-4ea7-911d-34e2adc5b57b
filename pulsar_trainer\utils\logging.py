"""
Logging utilities for GFNet pulsar classification.

This module provides structured logging functionality.
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime


class PulsarLogger:
    """Structured logger for pulsar classification training."""
    
    def __init__(self, name: str, log_dir: str = "logs", level: int = logging.INFO):
        """
        Initialize logger.
        
        Args:
            name: Logger name
            log_dir: Directory for log files
            level: Logging level
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # Prevent duplicate handlers
        if self.logger.handlers:
            return
        
        # Create log directory
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create timestamp for log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"{name}_{timestamp}.log"
        
        # File handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        self.logger.info(f"Logger initialized. Log file: {log_file}")
    
    def log_training_start(self, config: Dict[str, Any]) -> None:
        """Log training start information."""
        self.logger.info("=" * 60)
        self.logger.info("🚀 开始脉冲星分类训练")
        self.logger.info(f"📊 模态: {config['data']['modality']}")
        self.logger.info(f"🧠 模型: {config['model']['name']}")
        self.logger.info(f"📦 批次大小: {config['training']['batch_size']}")
        self.logger.info(f"🎯 学习率: {config['training']['learning_rate']}")
        self.logger.info(f"🔄 训练轮数: {config['training']['epochs']}")
        self.logger.info("=" * 60)
    
    def log_epoch_results(self, epoch: int, train_loss: float, val_loss: float, 
                         train_acc: float, val_acc: float) -> None:
        """Log epoch training results."""
        self.logger.info(
            f"Epoch {epoch:3d}: "
            f"Train Loss={train_loss:.4f}, Val Loss={val_loss:.4f}, "
            f"Train Acc={train_acc:.2f}%, Val Acc={val_acc:.2f}%"
        )
    
    def log_performance_check(self, results: Dict[str, Any]) -> None:
        """Log performance evaluation completion (internal tracking only)."""
        # Performance targets are tracked internally but not displayed
        # This maintains clean, objective logging output
        pass
    
    def log_model_info(self, model, total_params: int) -> None:
        """Log model information."""
        self.logger.info(f"📋 模型参数总数: {total_params:,}")
        self.logger.info(f"🏗️ 模型架构: {model.__class__.__name__}")
    
    def log_data_info(self, train_size: int, val_size: int, test_size: int) -> None:
        """Log dataset information."""
        self.logger.info(f"📚 数据集大小:")
        self.logger.info(f"  - 训练集: {train_size}")
        self.logger.info(f"  - 验证集: {val_size}")
        self.logger.info(f"  - 测试集: {test_size}")
        self.logger.info(f"  - 总计: {train_size + val_size + test_size}")
    
    def log_device_info(self, device_info: Dict[str, Any]) -> None:
        """Log device information."""
        if device_info['cuda_available']:
            self.logger.info(f"🖥️ 使用GPU: {device_info['device_name']}")
            memory_gb = device_info['total_memory'] / 1e9
            self.logger.info(f"💾 GPU内存: {memory_gb:.1f}GB")
        else:
            self.logger.info("🖥️ 使用CPU")
    
    def log_training_complete(self, best_val_acc: float, total_time: float) -> None:
        """Log training completion."""
        self.logger.info("=" * 60)
        self.logger.info("✅ 训练完成")
        self.logger.info(f"🏆 最佳验证准确率: {best_val_acc:.4f}")
        self.logger.info(f"⏱️ 总训练时间: {total_time:.2f}秒")
        self.logger.info("=" * 60)
    
    def log_evaluation_start(self) -> None:
        """Log evaluation start."""
        self.logger.info("🔍 开始最终评估...")
    
    def log_evaluation_results(self, results: Dict[str, Any]) -> None:
        """Log evaluation results."""
        metrics = results.get('basic_metrics', {})
        self.logger.info("📊 评估结果:")
        
        for metric, value in metrics.items():
            if isinstance(value, float):
                self.logger.info(f"  - {metric}: {value:.4f}")
            else:
                self.logger.info(f"  - {metric}: {value}")
    
    def log_error(self, error: Exception, context: str = "") -> None:
        """Log error with context."""
        if context:
            self.logger.error(f"❌ {context}: {error}")
        else:
            self.logger.error(f"❌ 错误: {error}")
    
    def log_warning(self, message: str) -> None:
        """Log warning message."""
        self.logger.warning(f"⚠️ {message}")
    
    def log_info(self, message: str) -> None:
        """Log info message."""
        self.logger.info(message)
    
    def log_debug(self, message: str) -> None:
        """Log debug message."""
        self.logger.debug(message)
