# CoAtNet Pulsar Classification Configuration
project:
  name: "pulsar-coatnet"
  version: "2.0.0"
  description: "CoAtNet-based pulsar classification system"

data:
  modality: "FPP"  # FPP, TPP
  channel_strategy: "single_channel"  # single_channel for 99.7% target
  data_root: "D:/pulsarSuanfa/datasets/HTRU"
  normalize: true
  num_workers: 4
  pin_memory: true

model:
  name: "coatnet"
  coatnet:
    model_size: "large"  # Use large model for 99.7% target
    num_blocks: [2, 2, 6, 8, 2]
    channels: [96, 128, 256, 512, 1024]
    block_types: ['C', 'C', 'T', 'T']  # More Transformer layers
    image_size: [64, 64]
    in_channels: 1  # Single channel input
    num_classes: 2
    dropout: 0.2  # Increased dropout for better regularization

training:
  batch_size: 16  # Smaller batch size for more gradient updates
  learning_rate: 0.0005  # Lower learning rate for 99.7% precision
  epochs: 200  # Extended training for 99.7% target
  weight_decay: 0.05  # Enhanced regularization for high precision
  optimizer: "adamw"  # AdamW works better with CoAtNet

  # Loss function configuration
  loss:
    type: "label_smoothing"  # Use label smoothing for 99.7% target
    smoothing: 0.1  # Label smoothing factor

  scheduler:
    type: "warmup_cosine"  # Use warmup cosine for better convergence
    warmup_epochs: 20  # Longer warmup for stability
    min_lr: 1e-8  # Lower minimum learning rate
  early_stopping:
    enabled: true
    patience: 20  # Increased patience for 99.7% target
    min_delta: 0.0001  # More precise improvement detection
    monitor: "f1_score"  # Monitor F1 score instead of accuracy
  mixed_precision: true
  gradient_clipping:
    enabled: true
    max_norm: 1.0

# Data augmentation optimized for CoAtNet
augmentation:
  enabled: true
  mixup:
    enabled: false
    alpha: 0.4  # Disabled - breaks pulsar signal physical properties
  cutmix:
    enabled: false
    alpha: 1.2  # Disabled - breaks frequency-phase continuity
  random_erasing:
    enabled: false
    probability: 0.3  # Disabled - removes important physical information
  rotation:
    enabled: false
    degrees: 15  # Disabled - no physical meaning for pulsar data
  noise:
    enabled: true
    std: 0.02  # Small noise injection

# Performance targets (for reference, not enforced)
performance_targets:
  accuracy: 0.997
  precision: 0.997
  recall: 0.997
  f1_score: 0.997
  false_positive_rate: 0.003

# Device configuration
device:
  use_cuda: true
  device_id: 0
  compile_model: false  # Disable compilation for stability
  benchmark: true
  deterministic: false

# Output configuration
output:
  base_dir: "outputs"
  save_best_model: true
  save_last_model: true
  save_optimizer: true
  log_interval: 10
  eval_interval: 1

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_to_file: true
  console_output: true

# Evaluation configuration
evaluation:
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "auc_roc"
    - "confusion_matrix"
  save_predictions: true
  save_probabilities: true
  generate_plots: true
  misclassification_analysis: true

# Visualization configuration
visualization:
  enabled: true
  save_training_curves: true
  save_confusion_matrix: true
  save_roc_curve: true
  save_pr_curve: true
  dpi: 300
  format: "png"
