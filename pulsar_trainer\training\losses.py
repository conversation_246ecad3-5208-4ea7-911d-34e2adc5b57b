"""
Advanced loss functions for high-precision pulsar classification.

This module implements specialized loss functions designed to achieve
99.7% accuracy in pulsar classification tasks.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class LabelSmoothingCrossEntropy(nn.Module):
    """
    Label Smoothing Cross Entropy Loss.
    
    Implements label smoothing to improve model calibration and generalization.
    Particularly effective for achieving high precision targets (99.7%).
    
    Args:
        smoothing (float): Label smoothing factor (default: 0.1)
        reduction (str): Reduction method ('mean', 'sum', 'none')
    """
    
    def __init__(self, smoothing: float = 0.1, reduction: str = 'mean'):
        super().__init__()
        self.smoothing = smoothing
        self.reduction = reduction
        
        if not 0.0 <= smoothing <= 1.0:
            raise ValueError(f"Smoothing must be in [0, 1], got {smoothing}")
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of label smoothing cross entropy.
        
        Args:
            pred: Predictions with shape (batch_size, num_classes)
            target: Ground truth labels with shape (batch_size,)
            
        Returns:
            Loss tensor
        """
        confidence = 1.0 - self.smoothing
        log_probs = F.log_softmax(pred, dim=-1)
        
        # Negative log likelihood for true class
        nll_loss = -log_probs.gather(dim=-1, index=target.unsqueeze(1))
        nll_loss = nll_loss.squeeze(1)
        
        # Smooth loss (uniform distribution over all classes)
        smooth_loss = -log_probs.mean(dim=-1)
        
        # Combine losses
        loss = confidence * nll_loss + self.smoothing * smooth_loss
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss


class FocalLoss(nn.Module):
    """
    Focal Loss for addressing class imbalance.
    
    Implements the focal loss from "Focal Loss for Dense Object Detection"
    to handle class imbalance in pulsar classification.
    
    Args:
        alpha (float): Weighting factor for rare class (default: 1.0)
        gamma (float): Focusing parameter (default: 2.0)
        reduction (str): Reduction method ('mean', 'sum', 'none')
    """
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of focal loss.
        
        Args:
            pred: Predictions with shape (batch_size, num_classes)
            target: Ground truth labels with shape (batch_size,)
            
        Returns:
            Loss tensor
        """
        # Compute cross entropy
        ce_loss = F.cross_entropy(pred, target, reduction='none')
        
        # Compute p_t
        pt = torch.exp(-ce_loss)
        
        # Compute focal loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class WeightedCrossEntropyLoss(nn.Module):
    """
    Weighted Cross Entropy Loss for class imbalance.
    
    Applies class weights to handle imbalanced datasets common in
    pulsar classification.
    
    Args:
        weights (torch.Tensor): Class weights
        reduction (str): Reduction method ('mean', 'sum', 'none')
    """
    
    def __init__(self, weights: torch.Tensor = None, reduction: str = 'mean'):
        super().__init__()
        self.weights = weights
        self.reduction = reduction
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of weighted cross entropy.
        
        Args:
            pred: Predictions with shape (batch_size, num_classes)
            target: Ground truth labels with shape (batch_size,)
            
        Returns:
            Loss tensor
        """
        return F.cross_entropy(pred, target, weight=self.weights, reduction=self.reduction)


class CombinedLoss(nn.Module):
    """
    Combined loss function for multi-objective optimization.
    
    Combines multiple loss functions with configurable weights to achieve
    optimal performance across multiple metrics.
    
    Args:
        losses (dict): Dictionary of loss functions and their weights
    """
    
    def __init__(self, losses: dict):
        super().__init__()
        self.losses = nn.ModuleDict()
        self.weights = {}
        
        for name, (loss_fn, weight) in losses.items():
            self.losses[name] = loss_fn
            self.weights[name] = weight
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of combined loss.
        
        Args:
            pred: Predictions with shape (batch_size, num_classes)
            target: Ground truth labels with shape (batch_size,)
            
        Returns:
            Combined loss tensor
        """
        total_loss = 0.0
        
        for name, loss_fn in self.losses.items():
            loss_value = loss_fn(pred, target)
            total_loss += self.weights[name] * loss_value
        
        return total_loss


def create_loss_function(loss_config: dict) -> nn.Module:
    """
    Factory function to create loss functions based on configuration.
    
    Args:
        loss_config: Configuration dictionary for loss function
        
    Returns:
        Configured loss function
    """
    loss_type = loss_config.get('type', 'cross_entropy')
    
    if loss_type == 'cross_entropy':
        return nn.CrossEntropyLoss()
    
    elif loss_type == 'label_smoothing':
        smoothing = loss_config.get('smoothing', 0.1)
        return LabelSmoothingCrossEntropy(smoothing=smoothing)
    
    elif loss_type == 'focal':
        alpha = loss_config.get('alpha', 1.0)
        gamma = loss_config.get('gamma', 2.0)
        return FocalLoss(alpha=alpha, gamma=gamma)
    
    elif loss_type == 'weighted':
        weights = loss_config.get('weights', None)
        if weights is not None:
            weights = torch.tensor(weights, dtype=torch.float32)
        return WeightedCrossEntropyLoss(weights=weights)
    
    elif loss_type == 'combined':
        loss_configs = loss_config.get('losses', {})
        losses = {}
        for name, config in loss_configs.items():
            loss_fn = create_loss_function(config['config'])
            weight = config.get('weight', 1.0)
            losses[name] = (loss_fn, weight)
        return CombinedLoss(losses)
    
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")


# Loss function registry for easy access
LOSS_REGISTRY = {
    'cross_entropy': nn.CrossEntropyLoss,
    'label_smoothing': LabelSmoothingCrossEntropy,
    'focal': FocalLoss,
    'weighted': WeightedCrossEntropyLoss,
    'combined': CombinedLoss
}


def get_available_losses() -> list:
    """Get list of available loss functions."""
    return list(LOSS_REGISTRY.keys())
