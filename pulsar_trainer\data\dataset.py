"""
Dataset classes for GFNet pulsar classification.

This module provides dataset classes for loading and preprocessing
pulsar data from FPP and TPP modalities.
"""

import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
from typing import Tuple, List, Optional, Dict, Any
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.transforms import ChannelTransformer


class DataLoadError(Exception):
    """Data loading related errors."""
    pass


class PulsarDataset(Dataset):
    """
    Dataset class for pulsar classification.
    
    Supports direct loading from directory structure:
    data_root/modality/split/*.npy
    
    Automatically discovers files and parses labels from filenames.
    """
    
    def __init__(
        self,
        modality: str,
        split: str,
        data_root: str,
        channel_strategy: str = 'physical',
        normalize: bool = True,
        transform: Optional[callable] = None
    ):
        """
        Initialize pulsar dataset.
        
        Args:
            modality: Data modality ('FPP' or 'TPP')
            split: Data split ('train', 'validation', 'test')
            data_root: Root directory containing data
            channel_strategy: Channel transformation strategy
            normalize: Whether to normalize data
            transform: Additional transforms to apply
        """
        self.modality = modality
        self.split = split
        self.data_root = Path(data_root)
        self.normalize = normalize
        self.transform = transform
        
        # Initialize channel transformer
        self.channel_transformer = ChannelTransformer(strategy=channel_strategy)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Validate inputs
        self._validate_inputs()
        
        # Discover files and labels
        self.file_paths, self.labels = self._discover_files()
        
        # Log dataset info
        self._log_dataset_info()
    
    def _validate_inputs(self) -> None:
        """Validate input parameters."""
        # Validate modality
        valid_modalities = ['FPP', 'TPP']
        if self.modality not in valid_modalities:
            raise ValueError(f"Invalid modality: {self.modality}. Must be one of {valid_modalities}")
        
        # Validate split
        valid_splits = ['train', 'validation', 'test']
        if self.split not in valid_splits:
            raise ValueError(f"Invalid split: {self.split}. Must be one of {valid_splits}")
        
        # Check data directory exists
        data_dir = self.data_root / self.modality / self.split
        if not data_dir.exists():
            raise DataLoadError(f"Data directory does not exist: {data_dir}")
    
    def _discover_files(self) -> Tuple[List[Path], List[int]]:
        """
        Discover data files and parse labels from filenames.
        
        Expected filename patterns:
        - Positive samples: *_positive.npy
        - Negative samples: *_negative.npy
        
        Returns:
            Tuple of (file_paths, labels)
        """
        data_dir = self.data_root / self.modality / self.split
        
        # Find all .npy files
        npy_files = list(data_dir.glob('*.npy'))
        
        if not npy_files:
            raise DataLoadError(f"No .npy files found in {data_dir}")
        
        file_paths = []
        labels = []
        
        for file_path in npy_files:
            filename = file_path.name.lower()
            
            if 'positive' in filename:
                labels.append(1)  # Pulsar
                file_paths.append(file_path)
            elif 'negative' in filename:
                labels.append(0)  # Non-pulsar
                file_paths.append(file_path)
            else:
                self.logger.warning(f"Skipping file with unknown label pattern: {file_path}")
                continue
        
        if not file_paths:
            raise DataLoadError(f"No valid files found in {data_dir}")
        
        return file_paths, labels
    
    def _log_dataset_info(self) -> None:
        """Log dataset information."""
        total_files = len(self.file_paths)
        positive_count = sum(self.labels)
        negative_count = total_files - positive_count
        
        self.logger.info(f"Dataset {self.modality}/{self.split}:")
        self.logger.info(f"  Total files: {total_files}")
        self.logger.info(f"  Positive samples: {positive_count}")
        self.logger.info(f"  Negative samples: {negative_count}")
        self.logger.info(f"  Balance ratio: {positive_count/total_files:.2f}")
    
    def __len__(self) -> int:
        """Return dataset size."""
        return len(self.file_paths)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get a data sample.
        
        Args:
            idx: Sample index
            
        Returns:
            Tuple of (data, label) tensors
        """
        try:
            # Load data
            data_path = self.file_paths[idx]
            data_1ch = np.load(data_path)
            
            # Validate data shape
            if data_1ch.shape != (64, 64, 1) and data_1ch.shape != (64, 64):
                raise DataLoadError(f"Invalid data shape: {data_1ch.shape}. Expected (64, 64, 1) or (64, 64)")
            
            # Transform to 3-channel format
            data_3ch = self.channel_transformer.transform(data_1ch, self.modality)
            
            # Normalize if requested
            if self.normalize:
                data_3ch = self._normalize_data(data_3ch)
            
            # Apply additional transforms
            if self.transform:
                data_3ch = self.transform(data_3ch)
            
            # Convert to tensors
            data_tensor = torch.from_numpy(data_3ch).float()
            label_tensor = torch.tensor(self.labels[idx], dtype=torch.long)
            
            return data_tensor, label_tensor
            
        except Exception as e:
            raise DataLoadError(f"Failed to load sample {idx} from {self.file_paths[idx]}: {e}")
    
    def _normalize_data(self, data: np.ndarray) -> np.ndarray:
        """
        Normalize data to [0, 1] range.

        RESTORED ORIGINAL METHOD: Simple channel-independent normalization
        that was used in the successful baseline implementation.

        Args:
            data: Input data with shape (3, 64, 64)

        Returns:
            Normalized data
        """
        # Apply channel-independent normalization (original successful strategy)
        normalized = data.copy()
        for i in range(data.shape[0]):
            channel = data[i]
            ch_min, ch_max = channel.min(), channel.max()
            if ch_max > ch_min:
                normalized[i] = (channel - ch_min) / (ch_max - ch_min)
            # If ch_max == ch_min, keep original values

        return normalized
    
    def get_class_distribution(self) -> Dict[str, int]:
        """Get class distribution."""
        positive_count = sum(self.labels)
        negative_count = len(self.labels) - positive_count
        
        return {
            'positive': positive_count,
            'negative': negative_count,
            'total': len(self.labels)
        }
    
    def get_sample_info(self, idx: int) -> Dict[str, Any]:
        """Get information about a specific sample."""
        return {
            'index': idx,
            'file_path': str(self.file_paths[idx]),
            'label': self.labels[idx],
            'label_name': 'positive' if self.labels[idx] == 1 else 'negative'
        }


def create_dataloaders(
    config: Dict[str, Any],
    batch_size: Optional[int] = None,
    num_workers: Optional[int] = None
) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create train, validation, and test dataloaders.
    
    Args:
        config: Configuration dictionary
        batch_size: Override batch size from config
        num_workers: Override num_workers from config
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    # Extract configuration
    data_config = config['data']
    training_config = config['training']
    
    batch_size = batch_size or training_config['batch_size']
    num_workers = num_workers or data_config.get('num_workers', 4)
    pin_memory = data_config.get('pin_memory', True)
    
    # Create datasets
    train_dataset = PulsarDataset(
        modality=data_config['modality'],
        split='train',
        data_root=data_config['data_root'],
        channel_strategy=data_config['channel_strategy'],
        normalize=data_config.get('normalize', True)
    )
    
    val_dataset = PulsarDataset(
        modality=data_config['modality'],
        split='validation',
        data_root=data_config['data_root'],
        channel_strategy=data_config['channel_strategy'],
        normalize=data_config.get('normalize', True)
    )
    
    test_dataset = PulsarDataset(
        modality=data_config['modality'],
        split='test',
        data_root=data_config['data_root'],
        channel_strategy=data_config['channel_strategy'],
        normalize=data_config.get('normalize', True)
    )
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        persistent_workers=num_workers > 0,
        prefetch_factor=2 if num_workers > 0 else 2
    )
    
    return train_loader, val_loader, test_loader


def get_dataset_info(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get comprehensive dataset information.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Dataset information dictionary
    """
    data_config = config['data']
    
    info = {
        'modality': data_config['modality'],
        'channel_strategy': data_config['channel_strategy'],
        'data_root': data_config['data_root'],
        'splits': {}
    }
    
    # Get info for each split
    for split in ['train', 'validation', 'test']:
        try:
            dataset = PulsarDataset(
                modality=data_config['modality'],
                split=split,
                data_root=data_config['data_root'],
                channel_strategy=data_config['channel_strategy']
            )
            
            distribution = dataset.get_class_distribution()
            info['splits'][split] = {
                'size': len(dataset),
                'positive': distribution['positive'],
                'negative': distribution['negative'],
                'balance_ratio': distribution['positive'] / distribution['total']
            }
            
        except Exception as e:
            info['splits'][split] = {'error': str(e)}
    
    return info
