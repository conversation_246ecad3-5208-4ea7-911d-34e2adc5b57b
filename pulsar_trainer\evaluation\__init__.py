"""
Evaluation module for GFNet pulsar classification.

This module provides evaluation metrics and analysis functionality.
"""

# Import existing evaluation functions from the main project
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../pulsar-identification-main'))

try:
    from evaluation.metrics import extended_outputanalysis
    __all__ = ['extended_outputanalysis']
except ImportError:
    print("Warning: Could not import extended_outputanalysis from main project")
    __all__ = []
