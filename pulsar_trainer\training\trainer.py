"""
Core training functionality for CoAtNet pulsar classification.

This module provides the main training class that orchestrates the entire
training process including data loading, model creation, training loops,
evaluation, and result saving.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, Any, Tuple, Optional, List
import time
from pathlib import Path
import logging
import json
from sklearn.metrics import precision_score, recall_score, f1_score

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.device import setup_device, optimize_memory
from utils.logging import PulsarLogger
from utils.config import create_output_dirs
from utils.visualization import create_visualizer
from utils.misclassification_analysis import create_misclassification_analyzer
from models.coatnet import create_coatnet_pulsar
from data.dataset import create_dataloaders, get_dataset_info
from training.losses import create_loss_function
from training.schedulers import create_scheduler
from training.evaluator import PulsarEvaluator


class TrainingError(Exception):
    """Training related errors."""
    pass


class PulsarTrainer:
    """
    Main trainer class for CoAtNet pulsar classification.

    Provides a complete training pipeline with support for:
    - Mixed precision training
    - Early stopping
    - Learning rate scheduling
    - Comprehensive evaluation
    - Result saving and visualization
    """

    def __init__(self, config: Dict[str, Any], logger: Optional[PulsarLogger] = None):
        """
        Initialize trainer.

        Args:
            config: Configuration dictionary
            logger: Optional logger instance
        """
        self.config = config
        self.logger = logger or PulsarLogger('pulsar_trainer')

        # Setup device
        device_config = config.get('device', {})
        self.device, self.is_gpu = setup_device(device_config.get('prefer_gpu', True))

        # Create output directories
        self.output_dirs = create_output_dirs(config)

        # Initialize training state
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.best_val_acc = 0.0
        self.best_val_f1 = 0.0
        self.best_val_precision = 0.0
        self.best_val_recall = 0.0
        self.early_stop_counter = 0
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': [],
            'val_f1': [],
            'val_precision': [],
            'val_recall': [],
            'learning_rates': []
        }

        # Setup model
        self._setup_model()

        # Setup data loaders
        self._setup_data()

        # Setup training components
        self._setup_training()

        # Setup evaluator
        self._setup_evaluator()

        # Setup visualization and analysis tools
        self._setup_analysis_tools()

        # Log initialization info
        self._log_initialization_info()

    def _setup_model(self) -> None:
        """Setup model and move to device."""
        try:
            # Create CoAtNet model
            model_config = self.config['model']['coatnet']
            model_size = model_config.get('model_size', 'base')
            self.model = create_coatnet_pulsar(model_size)
            self.model.to(self.device)

            # Enable compilation if requested and available
            device_config = self.config.get('device', {})
            if device_config.get('compile_model', False) and hasattr(torch, 'compile'):
                self.logger.log_info("启用PyTorch 2.0模型编译优化")
                self.model = torch.compile(self.model)

            # Get model summary
            self.model_summary = self._get_model_summary()

        except Exception as e:
            raise TrainingError(f"Failed to setup model: {e}")

    def _get_model_summary(self) -> Dict[str, Any]:
        """Get model summary information."""
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)

        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # Assuming float32
            'architecture': 'CoAtNet'
        }

    def _setup_data(self) -> None:
        """Setup data loaders."""
        try:
            # Create data loaders
            self.train_loader, self.val_loader, self.test_loader = create_dataloaders(self.config)

            # Get dataset info
            self.dataset_info = get_dataset_info(self.config)

        except Exception as e:
            raise TrainingError(f"Failed to setup data loaders: {e}")

    def _setup_training(self) -> None:
        """Setup training components (optimizer, scheduler, etc.)."""
        try:
            training_config = self.config['training']

            # Setup criterion
            self.criterion = self._setup_loss_function(training_config)

            # Setup optimizer
            optimizer_name = training_config.get('optimizer', 'adam').lower()
            if optimizer_name == 'adam':
                self.optimizer = optim.Adam(
                    self.model.parameters(),
                    lr=training_config['learning_rate'],
                    weight_decay=training_config.get('weight_decay', 0.0001)
                )
            elif optimizer_name == 'adamw':
                self.optimizer = optim.AdamW(
                    self.model.parameters(),
                    lr=training_config['learning_rate'],
                    weight_decay=training_config.get('weight_decay', 0.0001)
                )
            elif optimizer_name == 'sgd':
                self.optimizer = optim.SGD(
                    self.model.parameters(),
                    lr=training_config['learning_rate'],
                    momentum=training_config.get('momentum', 0.9),
                    weight_decay=training_config.get('weight_decay', 0.0001)
                )
            else:
                raise ValueError(f"Unsupported optimizer: {optimizer_name}")

            # Setup scheduler
            scheduler_config = training_config.get('scheduler', {})
            scheduler_type = scheduler_config.get('type', 'cosine').lower()

            # Add total_epochs to scheduler config for warmup schedulers
            scheduler_config['total_epochs'] = training_config['epochs']

            if scheduler_type in ['warmup_cosine', 'warmup_linear', 'warmup_step', 'polynomial']:
                # Use advanced schedulers with warmup
                self.scheduler = create_scheduler(self.optimizer, scheduler_config)
                self.logger.log_info(f"Using {scheduler_type} scheduler with warmup")
            elif scheduler_type == 'cosine':
                self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                    self.optimizer,
                    T_max=training_config['epochs'],
                    eta_min=training_config['learning_rate'] * 0.01
                )
            elif scheduler_type == 'step':
                self.scheduler = optim.lr_scheduler.StepLR(
                    self.optimizer,
                    step_size=scheduler_config.get('step_size', 10),
                    gamma=scheduler_config.get('gamma', 0.1)
                )
            elif scheduler_type == 'plateau':
                self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                    self.optimizer,
                    mode='min',
                    factor=scheduler_config.get('factor', 0.5),
                    patience=scheduler_config.get('patience', 5),
                    verbose=True
                )
            else:
                self.scheduler = None

            # Setup mixed precision training
            self.use_amp = training_config.get('mixed_precision', False) and self.is_gpu
            if self.use_amp:
                self.scaler = torch.amp.GradScaler('cuda')
                self.logger.log_info("启用混合精度训练")
            else:
                self.scaler = None

            # Gradient clipping configuration
            gradient_clip_config = training_config.get('gradient_clipping', {})
            self.use_gradient_clipping = gradient_clip_config.get('enabled', False)
            self.gradient_clip_value = gradient_clip_config.get('max_norm', 1.0)
            if self.use_gradient_clipping:
                self.logger.log_info(f"Gradient clipping enabled with max_norm={self.gradient_clip_value}")

            # Early stopping configuration
            early_stop_config = training_config.get('early_stopping', {})
            self.early_stopping_enabled = early_stop_config.get('enabled', True)
            self.early_stopping_patience = early_stop_config.get('patience', 10)
            self.early_stopping_min_delta = float(early_stop_config.get('min_delta', 0.001))
            self.early_stopping_monitor = early_stop_config.get('monitor', 'accuracy')
            if self.early_stopping_monitor not in ['accuracy', 'f1_score', 'precision', 'recall']:
                self.logger.log_warning(f"Unknown monitor metric: {self.early_stopping_monitor}, using accuracy")
                self.early_stopping_monitor = 'accuracy'

        except Exception as e:
            raise TrainingError(f"Failed to setup training components: {e}")

    def _setup_loss_function(self, training_config: dict) -> nn.Module:
        """
        Setup loss function based on configuration.

        Args:
            training_config: Training configuration dictionary

        Returns:
            Configured loss function
        """
        loss_config = training_config.get('loss', {'type': 'cross_entropy'})

        # Support for label smoothing
        if loss_config.get('type') == 'label_smoothing':
            smoothing = loss_config.get('smoothing', 0.1)
            self.logger.log_info(f"Using label smoothing with smoothing={smoothing}")
            return create_loss_function(loss_config)

        # Support for focal loss
        elif loss_config.get('type') == 'focal':
            alpha = loss_config.get('alpha', 1.0)
            gamma = loss_config.get('gamma', 2.0)
            self.logger.log_info(f"Using focal loss with alpha={alpha}, gamma={gamma}")
            return create_loss_function(loss_config)

        # Default cross entropy
        else:
            self.logger.log_info("Using standard cross entropy loss")
            return nn.CrossEntropyLoss()

    def _setup_evaluator(self) -> None:
        """Setup evaluator."""
        try:
            performance_targets = self.config['performance_targets']
            self.evaluator = PulsarEvaluator(performance_targets)
        except Exception as e:
            raise TrainingError(f"Failed to setup evaluator: {e}")

    def _setup_analysis_tools(self) -> None:
        """Setup visualization and analysis tools."""
        try:
            modality = self.output_dirs['modality']
            plot_dir = self.output_dirs['plot']

            # Setup visualizer
            self.visualizer = create_visualizer(plot_dir, modality)

            # Setup misclassification analyzer
            data_config = self.config['data']
            self.misclassification_analyzer = create_misclassification_analyzer(
                self.output_dirs['output'],
                modality,
                data_config['channel_strategy']
            )

        except Exception as e:
            raise TrainingError(f"Failed to setup analysis tools: {e}")

    def _log_initialization_info(self) -> None:
        """Log initialization information."""
        # Log device info
        device_info = {
            'cuda_available': torch.cuda.is_available(),
            'device_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU',
            'total_memory': torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else 0
        }
        self.logger.log_device_info(device_info)

        # Log model info
        total_params = self.model_summary['total_parameters']
        self.logger.log_model_info(self.model, total_params)

        # Log dataset info
        train_size = len(self.train_loader.dataset)
        val_size = len(self.val_loader.dataset)
        test_size = len(self.test_loader.dataset)
        self.logger.log_data_info(train_size, val_size, test_size)

    def train(self) -> Dict[str, List[float]]:
        """
        Main training loop.

        Returns:
            Training history dictionary
        """
        self.logger.log_training_start(self.config)
        start_time = time.time()

        try:
            for epoch in range(self.config['training']['epochs']):
                self.current_epoch = epoch

                # Training phase
                train_loss, train_acc = self._train_epoch()

                # Validation phase
                val_loss, val_metrics = self._validate_epoch()

                # Update learning rate
                if self.scheduler is not None:
                    if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                        self.scheduler.step(val_loss)
                    else:
                        self.scheduler.step()

                # Record history
                current_lr = self.optimizer.param_groups[0]['lr']
                self.training_history['train_loss'].append(train_loss)
                self.training_history['val_loss'].append(val_loss)
                self.training_history['train_acc'].append(train_acc)
                self.training_history['val_acc'].append(val_metrics['accuracy'])
                self.training_history['val_f1'].append(val_metrics['f1_score'])
                self.training_history['val_precision'].append(val_metrics['precision'])
                self.training_history['val_recall'].append(val_metrics['recall'])
                self.training_history['learning_rates'].append(current_lr)

                # Log epoch results
                self.logger.log_epoch_results(epoch + 1, train_loss, val_loss, train_acc, val_metrics['accuracy'])
                self.logger.log_info(f"Validation F1: {val_metrics['f1_score']:.4f}, Precision: {val_metrics['precision']:.4f}, Recall: {val_metrics['recall']:.4f}")

                # Multi-metric early stopping
                current_monitor_value = val_metrics[self.early_stopping_monitor]
                is_improvement = self._check_improvement(current_monitor_value, val_metrics, val_loss)

                if is_improvement:
                    self._update_best_metrics(val_metrics, val_loss)
                    self._save_model('best_model.pth')
                    self.early_stop_counter = 0
                else:
                    self.early_stop_counter += 1

                # Early stopping check
                if self._should_early_stop():
                    self.logger.log_info(f"早停触发，在第 {epoch + 1} 轮停止训练")
                    break

                # Memory cleanup
                if self.is_gpu:
                    optimize_memory()

            # Training completed
            total_time = time.time() - start_time
            self.logger.log_training_complete(self.best_val_acc, total_time)

            return self.training_history

        except Exception as e:
            self.logger.log_error(e, "训练过程中发生错误")
            raise TrainingError(f"Training failed: {e}")

    def _train_epoch(self) -> Tuple[float, float]:
        """
        Train for one epoch.

        Returns:
            Tuple of (average_loss, accuracy)
        """
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(self.train_loader):
            data, target = data.to(self.device), target.to(self.device)

            # Zero gradients
            self.optimizer.zero_grad()

            # Forward pass with mixed precision
            if self.use_amp:
                with torch.amp.autocast('cuda'):
                    output = self.model(data)
                    loss = self.criterion(output, target)

                # Backward pass
                self.scaler.scale(loss).backward()

                # Gradient clipping for mixed precision
                if self.use_gradient_clipping:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.gradient_clip_value)

                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                output = self.model(data)
                loss = self.criterion(output, target)
                loss.backward()

                # Gradient clipping for standard precision
                if self.use_gradient_clipping:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.gradient_clip_value)

                self.optimizer.step()

            # Statistics
            total_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()

        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100.0 * correct / total

        return avg_loss, accuracy

    def _validate_epoch(self) -> Tuple[float, Dict[str, float]]:
        """
        Validate for one epoch.

        Returns:
            Tuple of (average_loss, metrics_dict)
        """
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for data, target in self.val_loader:
                data, target = data.to(self.device), target.to(self.device)

                output = self.model(data)
                loss = self.criterion(output, target)

                total_loss += loss.item()
                _, predicted = torch.max(output.data, 1)

                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())

        avg_loss = total_loss / len(self.val_loader)

        # Calculate multiple metrics
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)

        accuracy = 100.0 * np.mean(all_predictions == all_targets)
        precision = 100.0 * precision_score(all_targets, all_predictions, average='weighted', zero_division=0)
        recall = 100.0 * recall_score(all_targets, all_predictions, average='weighted', zero_division=0)
        f1 = 100.0 * f1_score(all_targets, all_predictions, average='weighted', zero_division=0)

        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1
        }

        return avg_loss, metrics

    def _check_improvement(self, current_value: float, val_metrics: Dict[str, float], val_loss: float) -> bool:
        """
        Check if current validation metrics represent an improvement.

        Args:
            current_value: Current value of the monitored metric
            val_metrics: All validation metrics
            val_loss: Validation loss

        Returns:
            True if improvement detected
        """
        if self.early_stopping_monitor == 'accuracy':
            best_value = self.best_val_acc
        elif self.early_stopping_monitor == 'f1_score':
            best_value = self.best_val_f1
        elif self.early_stopping_monitor == 'precision':
            best_value = self.best_val_precision
        elif self.early_stopping_monitor == 'recall':
            best_value = self.best_val_recall
        else:
            best_value = self.best_val_acc

        return current_value > best_value + self.early_stopping_min_delta

    def _update_best_metrics(self, val_metrics: Dict[str, float], val_loss: float) -> None:
        """
        Update best validation metrics.

        Args:
            val_metrics: Current validation metrics
            val_loss: Current validation loss
        """
        self.best_val_acc = val_metrics['accuracy']
        self.best_val_f1 = val_metrics['f1_score']
        self.best_val_precision = val_metrics['precision']
        self.best_val_recall = val_metrics['recall']
        self.best_val_loss = val_loss

    def _should_early_stop(self) -> bool:
        """Check if early stopping should be triggered."""
        if not self.early_stopping_enabled:
            return False

        return self.early_stop_counter >= self.early_stopping_patience

    def _save_model(self, filename: str) -> None:
        """Save model checkpoint."""
        model_path = self.output_dirs['model'] / filename

        # Prepare checkpoint
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_val_acc': self.best_val_acc,
            'best_val_loss': self.best_val_loss,
            'training_history': self.training_history,
            'config': self.config,
            'model_summary': self.model_summary
        }

        if self.scheduler is not None:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()

        if self.scaler is not None:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()

        torch.save(checkpoint, model_path)
        self.logger.log_info(f"模型已保存: {model_path}")

    def load_model(self, filename: str) -> None:
        """Load model checkpoint."""
        model_path = self.output_dirs['model'] / filename

        if not model_path.exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")

        checkpoint = torch.load(model_path, map_location=self.device)

        # Load model state
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # Load training state
        self.current_epoch = checkpoint['epoch']
        self.best_val_acc = checkpoint['best_val_acc']
        self.best_val_loss = checkpoint['best_val_loss']
        self.training_history = checkpoint['training_history']

        # Load scheduler and scaler if available
        if self.scheduler is not None and 'scheduler_state_dict' in checkpoint:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        if self.scaler is not None and 'scaler_state_dict' in checkpoint:
            self.scaler.load_state_dict(checkpoint['scaler_state_dict'])

        self.logger.log_info(f"模型已加载: {model_path}")

    def evaluate(self) -> Dict[str, Any]:
        """
        Evaluate model on test set.

        Returns:
            Evaluation results dictionary
        """
        self.logger.log_evaluation_start()

        # Load best model
        try:
            self.load_model('best_model.pth')
        except FileNotFoundError:
            self.logger.log_warning("未找到最佳模型文件，使用当前模型进行评估")

        # Evaluate on test set with comprehensive misclassification analysis
        results = self.evaluator.evaluate(self.model, self.test_loader, self.device,
                                         output_dir=self.output_dirs['result'])

        # Log results
        self.logger.log_evaluation_results(results)
        self.logger.log_performance_check(results)

        return results

    def save_results(self, train_history: Dict[str, List[float]], eval_results: Dict[str, Any]) -> None:
        """
        Save training and evaluation results with enhanced visualizations.

        Args:
            train_history: Training history
            eval_results: Evaluation results
        """
        # Save training history
        history_file = self.output_dirs['result'] / 'training_history.json'
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(train_history, f, indent=2, ensure_ascii=False)

        # Save evaluation results
        self.evaluator.save_results(eval_results, self.output_dirs['result'])

        # Create visualizations
        self.visualizer.create_all_plots(train_history, eval_results)

        # Perform misclassification analysis
        if hasattr(self, 'test_loader'):
            try:
                misclass_results = self.misclassification_analyzer.analyze_misclassifications(
                    self.test_loader.dataset,
                    eval_results['predictions'],
                    eval_results['targets'],
                    eval_results['probabilities']
                )

                # Save improvement suggestions
                suggestions = self.misclassification_analyzer.generate_improvement_suggestions(misclass_results)
                suggestions_file = self.output_dirs['result'] / 'improvement_suggestions.txt'
                with open(suggestions_file, 'w', encoding='utf-8') as f:
                    f.write("=== Model Improvement Suggestions ===\n\n")
                    for i, suggestion in enumerate(suggestions, 1):
                        f.write(f"{i}. {suggestion}\n")

            except Exception as e:
                self.logger.log_warning(f"Misclassification analysis failed: {e}")

        # Save configuration
        config_file = self.output_dirs['result'] / 'config.json'
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)

        # Save model summary
        summary_file = self.output_dirs['result'] / 'model_summary.json'
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(self.model_summary, f, indent=2, ensure_ascii=False)

        self.logger.log_info(f"所有结果已保存到: {self.output_dirs['result']}")
        self.logger.log_info(f"可视化图表已保存到: {self.output_dirs['plot']}")

    def get_training_summary(self) -> Dict[str, Any]:
        """Get training summary."""
        return {
            'config': self.config,
            'model_summary': self.model_summary,
            'dataset_info': self.dataset_info,
            'training_history': self.training_history,
            'best_validation_accuracy': self.best_val_acc,
            'best_validation_loss': self.best_val_loss,
            'total_epochs_trained': self.current_epoch + 1,
            'early_stopped': self.early_stop_counter >= self.early_stopping_patience
        }
