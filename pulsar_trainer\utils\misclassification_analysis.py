"""
Misclassification analysis utilities for GFNet pulsar classification.

This module provides functions to analyze misclassified samples,
identify patterns, and generate detailed reports for model improvement.
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, List
from pathlib import Path
import logging
import json

from data.transforms import ChannelTransformer


class MisclassificationAnalyzer:
    """Analyzer for misclassified pulsar samples."""
    
    def __init__(self, output_dir: Path, modality: str, channel_strategy: str = 'physical'):
        """
        Initialize misclassification analyzer.
        
        Args:
            output_dir: Directory to save analysis results
            modality: Data modality (FPP or TPP)
            channel_strategy: Channel transformation strategy
        """
        self.output_dir = Path(output_dir)
        self.modality = modality
        self.channel_strategy = channel_strategy
        self.logger = logging.getLogger(__name__)
        
        # Create analysis subdirectory
        self.analysis_dir = self.output_dir / 'misclassification_analysis'
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize channel transformer for visualization
        self.transformer = ChannelTransformer(strategy=channel_strategy)
    
    def analyze_misclassifications(self, dataset, predictions: np.ndarray,
                                 targets: np.ndarray, probabilities: np.ndarray) -> Dict[str, Any]:
        """
        Perform focused misclassification analysis on false negatives (missed pulsars).

        Args:
            dataset: Dataset object with file paths and labels
            predictions: Model predictions
            targets: True labels
            probabilities: Model output probabilities

        Returns:
            Analysis results dictionary focused on false negatives
        """
        self.logger.info("Starting false negative analysis (missed pulsars)...")

        # Identify misclassified samples
        misclassified_indices = np.where(predictions != targets)[0]

        # Focus only on false negatives (missed pulsars)
        false_negatives = []  # Predicted non-pulsar, actually pulsar

        for idx in misclassified_indices:
            # Only process false negatives (missed pulsars)
            if targets[idx] == 1 and predictions[idx] == 0:
                sample_info = {
                    'index': int(idx),
                    'file_path': str(dataset.file_paths[idx]),
                    'true_label': int(targets[idx]),
                    'predicted_label': int(predictions[idx]),
                    'confidence': float(probabilities[idx, predictions[idx]]),
                    'pulsar_probability': float(probabilities[idx, 1])
                }
                false_negatives.append(sample_info)

        # Calculate statistics focused on false negatives
        total_samples = len(targets)
        total_pulsars = np.sum(targets == 1)
        fn_count = len(false_negatives)

        analysis_results = {
            'summary': {
                'total_samples': total_samples,
                'total_pulsars': total_pulsars,
                'missed_pulsars': fn_count,
                'false_negative_rate': fn_count / total_pulsars if total_pulsars > 0 else 0,
                'pulsar_detection_rate': (total_pulsars - fn_count) / total_pulsars if total_pulsars > 0 else 0
            },
            'false_negatives': false_negatives,
            'confidence_analysis': self._analyze_false_negative_patterns(false_negatives)
        }

        # Save detailed analysis
        self._save_analysis_results(analysis_results)

        # Generate visualizations focused on false negatives
        self._create_false_negative_visualizations(analysis_results, dataset)

        self.logger.info(f"False negative analysis completed. Results saved to: {self.analysis_dir}")

        return analysis_results
    
    def _analyze_false_negative_patterns(self, false_negatives: List[Dict]) -> Dict[str, Any]:
        """
        Analyze confidence patterns in false negative samples (missed pulsars).

        Args:
            false_negatives: List of false negative samples

        Returns:
            False negative confidence analysis results
        """
        if not false_negatives:
            return {
                'count': 0,
                'mean_confidence': 0,
                'std_confidence': 0,
                'min_confidence': 0,
                'max_confidence': 0,
                'high_confidence_count': 0,
                'confidence_ranges': {
                    'very_low': 0,    # < 0.2
                    'low': 0,         # 0.2-0.4
                    'medium': 0,      # 0.4-0.6
                    'high': 0,        # 0.6-0.8
                    'very_high': 0    # > 0.8
                }
            }

        fn_confidences = [sample['confidence'] for sample in false_negatives]
        pulsar_probs = [sample['pulsar_probability'] for sample in false_negatives]

        # Analyze confidence ranges
        confidence_ranges = {
            'very_low': sum(1 for c in fn_confidences if c < 0.2),
            'low': sum(1 for c in fn_confidences if 0.2 <= c < 0.4),
            'medium': sum(1 for c in fn_confidences if 0.4 <= c < 0.6),
            'high': sum(1 for c in fn_confidences if 0.6 <= c < 0.8),
            'very_high': sum(1 for c in fn_confidences if c >= 0.8)
        }

        analysis = {
            'count': len(fn_confidences),
            'mean_confidence': np.mean(fn_confidences),
            'std_confidence': np.std(fn_confidences),
            'min_confidence': np.min(fn_confidences),
            'max_confidence': np.max(fn_confidences),
            'high_confidence_count': sum(1 for c in fn_confidences if c > 0.8),
            'confidence_ranges': confidence_ranges,
            'mean_pulsar_probability': np.mean(pulsar_probs),
            'low_pulsar_prob_count': sum(1 for p in pulsar_probs if p < 0.3)
        }

        return analysis
    
    def _save_analysis_results(self, analysis_results: Dict[str, Any]) -> None:
        """
        Save false negative analysis results to files.

        Args:
            analysis_results: Analysis results dictionary focused on false negatives
        """
        # Save summary report focused on missed pulsars
        summary_file = self.analysis_dir / 'misclassification_summary.txt'
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"=== Pulsar Detection Analysis - {self.modality} Modality ===\n\n")

            summary = analysis_results['summary']
            f.write("Detection Performance:\n")
            f.write(f"  Total Samples: {summary['total_samples']}\n")
            f.write(f"  Total Pulsars: {summary['total_pulsars']}\n")
            f.write(f"  Missed Pulsars: {summary['missed_pulsars']}\n")
            f.write(f"  Detection Rate: {summary['pulsar_detection_rate']:.4f}\n")
            f.write(f"  Miss Rate: {summary['false_negative_rate']:.4f}\n\n")

            # False negative confidence analysis
            conf_analysis = analysis_results['confidence_analysis']
            f.write("Missed Pulsar Analysis:\n")
            f.write(f"  Count: {conf_analysis['count']}\n")
            if conf_analysis['count'] > 0:
                f.write(f"  Mean Confidence: {conf_analysis['mean_confidence']:.4f}\n")
                f.write(f"  Mean Pulsar Probability: {conf_analysis['mean_pulsar_probability']:.4f}\n")
                f.write(f"  High Confidence Misses (>0.8): {conf_analysis['high_confidence_count']}\n")
                f.write(f"  Low Pulsar Probability (<0.3): {conf_analysis['low_pulsar_prob_count']}\n")
            else:
                f.write("  No missed pulsars detected!\n")
            f.write("\n")

            # Confidence distribution
            if conf_analysis['count'] > 0:
                ranges = conf_analysis['confidence_ranges']
                f.write("Confidence Distribution of Missed Pulsars:\n")
                f.write(f"  Very Low (<0.2): {ranges['very_low']}\n")
                f.write(f"  Low (0.2-0.4): {ranges['low']}\n")
                f.write(f"  Medium (0.4-0.6): {ranges['medium']}\n")
                f.write(f"  High (0.6-0.8): {ranges['high']}\n")
                f.write(f"  Very High (>0.8): {ranges['very_high']}\n")

        # Save detailed JSON report
        json_file = self.analysis_dir / 'misclassification_details.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            # Convert numpy types to native Python types for JSON serialization
            json_serializable_results = self._convert_numpy_types(analysis_results)
            json.dump(json_serializable_results, f, indent=2, ensure_ascii=False)

        # Save missed pulsar file list
        fn_files = [sample['file_path'] for sample in analysis_results['false_negatives']]

        with open(self.analysis_dir / 'false_negatives_files.txt', 'w') as f:
            f.write('\n'.join(fn_files))
    
    def _create_false_negative_visualizations(self, analysis_results: Dict[str, Any],
                                            dataset) -> None:
        """
        Create visualizations focused on false negative analysis.

        Args:
            analysis_results: Analysis results focused on false negatives
            dataset: Dataset object
        """
        # Plot false negative confidence distribution
        self._plot_false_negative_confidence_distribution(analysis_results)

        # Plot false negative sample visualizations
        self._plot_false_negatives_only(analysis_results, dataset)
    
    def _plot_false_negative_confidence_distribution(self, analysis_results: Dict[str, Any]) -> None:
        """Plot confidence distribution for false negative samples (missed pulsars)."""
        fn_confidences = [s['confidence'] for s in analysis_results['false_negatives']]

        if not fn_confidences:
            self.logger.info("No false negatives to plot confidence distribution")
            return

        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        # Plot histogram
        ax.hist(fn_confidences, bins=20, alpha=0.7, color='red', edgecolor='black')
        ax.axvline(np.mean(fn_confidences), color='darkred', linestyle='--',
                   label=f'Mean: {np.mean(fn_confidences):.3f}')

        # Add confidence range markers
        ax.axvline(0.2, color='gray', linestyle=':', alpha=0.5, label='Low threshold')
        ax.axvline(0.8, color='gray', linestyle=':', alpha=0.5, label='High threshold')

        ax.set_title(f'Missed Pulsars - Confidence Distribution ({self.modality})')
        ax.set_xlabel('Model Confidence (Non-pulsar prediction)')
        ax.set_ylabel('Count')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Add statistics text
        stats_text = f'Total: {len(fn_confidences)}\nMean: {np.mean(fn_confidences):.3f}\nStd: {np.std(fn_confidences):.3f}'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        save_path = self.analysis_dir / f'confidence_distributions_{self.modality.lower()}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_false_negatives_only(self, analysis_results: Dict[str, Any],
                                 dataset, max_samples: int = 6) -> None:
        """
        Plot visualizations of false negative samples (missed pulsars) only.

        Args:
            analysis_results: Analysis results focused on false negatives
            dataset: Dataset object
            max_samples: Maximum number of samples to visualize
        """
        false_negatives = analysis_results['false_negatives']

        if not false_negatives:
            self.logger.info("No false negatives to visualize")
            return

        n_samples = min(len(false_negatives), max_samples)
        fig, axes = plt.subplots(1, n_samples, figsize=(4*n_samples, 4))

        # Handle single sample case
        if n_samples == 1:
            axes = [axes]

        for i, sample in enumerate(false_negatives[:n_samples]):
            try:
                # Load original data
                file_path = Path(sample['file_path'])
                data_1ch = np.load(file_path)

                # Plot only original data (single visualization)
                axes[i].imshow(data_1ch.squeeze(), cmap='viridis', aspect='auto')
                axes[i].set_title(f'Missed Pulsar #{i+1}\n'
                                f'Conf: {sample["confidence"]:.3f}\n'
                                f'P(pulsar): {sample["pulsar_probability"]:.3f}')
                axes[i].axis('off')

            except Exception as e:
                self.logger.warning(f"Failed to visualize sample {sample['file_path']}: {e}")
                axes[i].text(0.5, 0.5, 'Load Error', ha='center', va='center',
                           transform=axes[i].transAxes)
                axes[i].set_title(f'Missed Pulsar #{i+1}\nLoad Error')
                axes[i].axis('off')

        plt.suptitle(f'Missed Pulsars Analysis - {self.modality} Modality', fontsize=14, y=0.98)
        plt.tight_layout()
        save_path = self.analysis_dir / f'misclassified_samples_{self.modality.lower()}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_improvement_suggestions(self, analysis_results: Dict[str, Any]) -> List[str]:
        """
        Generate suggestions for improving pulsar detection based on false negative analysis.

        Args:
            analysis_results: Analysis results focused on false negatives

        Returns:
            List of improvement suggestions for reducing missed pulsars
        """
        suggestions = []

        summary = analysis_results['summary']
        conf_analysis = analysis_results['confidence_analysis']

        # High false negative rate (missed pulsars)
        if summary['false_negative_rate'] > 0.05:
            suggestions.append("High pulsar miss rate detected. Consider lowering classification threshold or improving model sensitivity")

        # High confidence false negatives
        fn_high_conf = conf_analysis['high_confidence_count']
        if fn_high_conf > 0:
            suggestions.append(f"Investigate {fn_high_conf} high-confidence missed pulsars for systematic detection failures")

        # Low pulsar probability predictions
        low_prob_count = conf_analysis['low_pulsar_prob_count']
        if low_prob_count > 0:
            suggestions.append(f"{low_prob_count} missed pulsars had very low pulsar probability (<0.3). Consider data quality or feature extraction improvements")

        # Confidence distribution analysis
        ranges = conf_analysis['confidence_ranges']
        if ranges['very_high'] > 0:
            suggestions.append(f"{ranges['very_high']} pulsars missed with very high confidence. Review these cases for potential labeling errors or edge cases")

        if ranges['medium'] + ranges['high'] > ranges['low'] + ranges['very_low']:
            suggestions.append("Many missed pulsars have medium-to-high confidence. Consider ensemble methods or model calibration")

        # Overall detection performance
        if summary['pulsar_detection_rate'] < 0.95:
            suggestions.append("Detection rate below 95%. Consider architecture improvements, data augmentation, or threshold optimization")

        # Specific recommendations based on miss patterns
        if conf_analysis['mean_confidence'] > 0.6:
            suggestions.append("High average confidence in missed pulsars suggests systematic bias. Review training data balance and loss function")

        return suggestions

    def _convert_numpy_types(self, obj):
        """
        Recursively convert numpy types to native Python types for JSON serialization.

        Args:
            obj: Object that may contain numpy types

        Returns:
            Object with numpy types converted to native Python types
        """
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_types(item) for item in obj]
        else:
            return obj


def create_misclassification_analyzer(output_dir: Path, modality: str,
                                    channel_strategy: str = 'physical') -> MisclassificationAnalyzer:
    """
    Create a misclassification analyzer instance.
    
    Args:
        output_dir: Output directory
        modality: Data modality
        channel_strategy: Channel transformation strategy
        
    Returns:
        MisclassificationAnalyzer instance
    """
    return MisclassificationAnalyzer(output_dir, modality, channel_strategy)
