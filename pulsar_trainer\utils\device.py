"""
Device management for GFNet pulsar classification.

This module provides functions to setup and manage compute devices.
"""

import torch
from typing import Tuple
import logging


def setup_device(prefer_gpu: bool = True) -> Tuple[torch.device, bool]:
    """
    Setup compute device for training.
    
    Args:
        prefer_gpu: Whether to prefer GPU if available
        
    Returns:
        Tuple of (device, is_gpu)
    """
    logger = logging.getLogger(__name__)
    
    if prefer_gpu and torch.cuda.is_available():
        device = torch.device('cuda')
        is_gpu = True
        
        # Log GPU information
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        logger.info(f"Using GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        
        # Set memory allocation strategy
        torch.cuda.empty_cache()
        
    else:
        device = torch.device('cpu')
        is_gpu = False
        logger.info("Using CPU")
        
        if prefer_gpu:
            logger.warning("GPU was preferred but not available, falling back to CPU")
    
    return device, is_gpu


def optimize_memory():
    """Optimize GPU memory usage."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()


def get_device_info() -> dict:
    """
    Get detailed device information.
    
    Returns:
        Dictionary with device information
    """
    info = {
        'cuda_available': torch.cuda.is_available(),
        'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
        'current_device': torch.cuda.current_device() if torch.cuda.is_available() else None,
    }
    
    if torch.cuda.is_available():
        props = torch.cuda.get_device_properties(0)
        info.update({
            'device_name': props.name,
            'total_memory': props.total_memory,
            'major': props.major,
            'minor': props.minor,
            'multi_processor_count': props.multi_processor_count
        })
    
    return info


def set_deterministic(seed: int = 42):
    """
    Set deterministic behavior for reproducible results.
    
    Args:
        seed: Random seed
    """
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def enable_optimizations():
    """Enable PyTorch optimizations."""
    # Enable cuDNN auto-tuner
    torch.backends.cudnn.benchmark = True
    
    # Enable TensorFloat-32 (TF32) on Ampere GPUs
    if torch.cuda.is_available():
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
