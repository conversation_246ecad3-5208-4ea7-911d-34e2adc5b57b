{"project": {"name": "pulsar-coatnet", "version": "2.0.0", "description": "CoAtNet-based pulsar classification system"}, "data": {"modality": "TPP", "channel_strategy": "single_channel", "data_root": "D:/pulsarSuanfa/datasets/HTRU", "normalize": true, "num_workers": 4, "pin_memory": true}, "model": {"name": "coatnet", "coatnet": {"model_size": "large", "num_blocks": [2, 2, 6, 8, 2], "channels": [96, 128, 256, 512, 1024], "block_types": ["C", "C", "T", "T"], "image_size": [64, 64], "in_channels": 1, "num_classes": 2, "dropout": 0.2}}, "training": {"batch_size": 16, "learning_rate": 0.0005, "epochs": 200, "weight_decay": 0.05, "optimizer": "adamw", "loss": {"type": "label_smoothing", "smoothing": 0.1}, "scheduler": {"type": "warmup_cosine", "warmup_epochs": 20, "min_lr": 1e-08, "total_epochs": 200}, "early_stopping": {"enabled": true, "patience": 20, "min_delta": 0.0001, "monitor": "f1_score"}, "mixed_precision": true, "gradient_clipping": {"enabled": true, "max_norm": 1.0}}, "augmentation": {"enabled": true, "mixup": {"enabled": false, "alpha": 0.4}, "cutmix": {"enabled": false, "alpha": 1.2}, "random_erasing": {"enabled": false, "probability": 0.3}, "rotation": {"enabled": false, "degrees": 15}, "noise": {"enabled": true, "std": 0.02}}, "performance_targets": {"accuracy": 99.7, "precision": 99.7, "recall": 99.7, "f1_score": 99.7, "false_positive_rate": 0.3}, "device": {"use_cuda": true, "device_id": 0, "compile_model": false, "benchmark": true, "deterministic": false}, "output": {"base_dir": "outputs", "save_best_model": true, "save_last_model": true, "save_optimizer": true, "log_interval": 10, "eval_interval": 1}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "save_to_file": true, "console_output": true}, "evaluation": {"metrics": ["accuracy", "precision", "recall", "f1_score", "auc_roc", "confusion_matrix"], "save_predictions": true, "save_probabilities": true, "generate_plots": true, "misclassification_analysis": true}, "visualization": {"enabled": true, "save_training_curves": true, "save_confusion_matrix": true, "save_roc_curve": true, "save_pr_curve": true, "dpi": 300, "format": "png"}}