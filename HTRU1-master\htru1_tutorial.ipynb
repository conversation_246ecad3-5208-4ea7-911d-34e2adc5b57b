{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This script follows [the standard CIFAR10 Pytorch example](https://pytorch.org/tutorials/beginner/blitz/cifar10_tutorial.html). It treats the three \"channels\" in the HTRU1 dataset as RGB.\n", "\n", "The steps are:\n", "\n", "1. Load and normalizing the HTRU1 training and test datasets using torchvision\n", "2. Define a Convolutional Neural Network\n", "3. Define a loss function\n", "4. Train the network on the training data\n", "5. Test the network on the test data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First import some standard python libraries for plotting stuff and handling arrays:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then import the pytorch and torchvision libraries:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torchvision\n", "import torchvision.transforms as transforms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then import the pytorch neural network stuff:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import torch.nn as nn\n", "import torch.nn.functional as F"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then import the oprimization library from pytorch:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import torch.optim as optim"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally import the HTRU1 pytorch dataset class. This is not provided with pytorch, you need to [grab it from the HTRU1 github](\n", "https://raw.githubusercontent.com/as595/HTRU1/master/htru1.py)."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from htru1 import HTRU1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The output of torchvision datasets are PILImage images of range [0, 1]. We transform them to Tensors of normalized range [-1, 1]. "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["transform = transforms.Compose([\n", "    transforms.RandomHorizontalFlip(), # randomly flip \n", "    transforms.RandomRotation(10), # randomly rotate\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))\n", "    ])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load in the training and test datasets. The first time you do this it will download the data to your working directory, but once the data is there it will just use it without repeating the download."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files already downloaded and verified\n"]}], "source": ["trainset = HTRU1(root='./data', train=True, download=True, transform=transform)\n", "trainloader = torch.utils.data.DataLoader(trainset, batch_size=4, shuffle=True, num_workers=2)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files already downloaded and verified\n"]}], "source": ["testset = HTRU1(root='./data', train=False, download=True, transform=transform)\n", "testloader = torch.utils.data.DataLoader(testset, batch_size=4, shuffle=False, num_workers=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are two classes in this dataset: pulsar and nonpulsar (i.e. RFI):"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["classes = ('pulsar', 'nonpulsar')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A little function to display images nicely:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def imshow(img):\n", "    img = img / 2 + 0.5     # unnormalize\n", "    npimg = img.numpy()\n", "    plt.imshow(np.transpose(npimg, (1, 2, 0)))\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Take a look at some randomly selected samples to see how they appear:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# get some random training images\n", "dataiter = iter(trainloader)\n", "images, labels = dataiter.next()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["nonpulsar nonpulsar nonpulsar nonpulsar\n"]}], "source": ["# show images\n", "imshow(torchvision.utils.make_grid(images))\n", "# print labels\n", "print(' '.join('%5s' % classes[labels[j]] for j in range(4)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define a neural network that takes 3-channel images as input:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["class Net(nn.Module):\n", "    def __init__(self):\n", "        super(Net, self).__init__()\n", "        self.conv1 = nn.Conv2d(3, 6, 5)\n", "        self.pool = nn.MaxPool2d(2, 2)\n", "        self.conv2 = nn.Conv2d(6, 16, 5)\n", "        self.fc1 = nn.Linear(16 * 5 * 5, 120)\n", "        self.fc2 = nn.<PERSON><PERSON>(120, 84)\n", "        self.fc3 = nn.<PERSON><PERSON>(84, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))\n", "        x = x.view(-1, 16 * 5 * 5)\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["net = Net()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll use Classification Cross-Entropy loss and SGD with momentum for optimization:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.SGD(net.parameters(), lr=0.001, momentum=0.9)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Run a few epochs of training:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1,  2000] loss: 0.192\n", "[1,  4000] loss: 0.064\n", "[1,  6000] loss: 0.057\n", "[1,  8000] loss: 0.053\n", "[1, 10000] loss: 0.051\n", "[1, 12000] loss: 0.051\n", "[2,  2000] loss: 0.034\n", "[2,  4000] loss: 0.041\n", "[2,  6000] loss: 0.036\n", "[2,  8000] loss: 0.033\n", "[2, 10000] loss: 0.036\n", "[2, 12000] loss: 0.036\n", "Finished Training\n"]}], "source": ["nepoch = 2  # number of epochs\n", "\n", "for epoch in range(nepoch):  # loop over the dataset multiple times\n", "\n", "    running_loss = 0.0\n", "    for i, data in enumerate(trainloader, 0):\n", "        # get the inputs\n", "        inputs, labels = data\n", "\n", "        # zero the parameter gradients\n", "        optimizer.zero_grad()\n", "\n", "        # forward + backward + optimize\n", "        outputs = net(inputs)\n", "        loss = criterion(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        # print statistics\n", "        running_loss += loss.item()\n", "        if i % 2000 == 1999:    # print every 2000 mini-batches\n", "            print('[%d, %5d] loss: %.3f' %\n", "                  (epoch + 1, i + 1, running_loss / 2000))\n", "            running_loss = 0.0\n", "\n", "print('Finished Training')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we'll try out a couple of test samples just for visual kicks. First load them up and take a look at the true labels:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["dataiter = iter(testloader)\n", "images, labels = dataiter.next()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["GroundTruth:  pulsar pulsar pulsar pulsar\n"]}], "source": ["# print images\n", "imshow(torchvision.utils.make_grid(images))\n", "print('GroundTruth: ', ' '.join('%5s' % classes[labels[j]] for j in range(4)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then see what the network predicts that they are:"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["outputs = net(images)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predicted:  nonpulsar pulsar pulsar pulsar\n"]}], "source": ["_, predicted = torch.max(outputs, 1)\n", "\n", "print('Predicted: ', ' '.join('%5s' % classes[predicted[j]] for j in range(4)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now calculate the overall accuracy of the network on **all** the test images:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy of the network on the 10000 test images: 99 %\n"]}], "source": ["correct = 0\n", "total = 0\n", "with torch.no_grad():\n", "    for data in testloader:\n", "        images, labels = data\n", "        outputs = net(images)\n", "        _, predicted = torch.max(outputs.data, 1)\n", "        total += labels.size(0)\n", "        correct += (predicted == labels).sum().item()\n", "\n", "print('Accuracy of the network on the 10000 test images: %d %%' % (100 * correct / total))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is a seriously imbalanced dataset, so let's take a look at the accuracy for individual classes:"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["class_correct = list(0. for i in range(10))\n", "class_total = list(0. for i in range(10))\n", "with torch.no_grad():\n", "    for data in testloader:\n", "        images, labels = data\n", "        outputs = net(images)\n", "        _, predicted = torch.max(outputs, 1)\n", "        c = (predicted == labels).squeeze()\n", "        for i in range(4):\n", "            label = labels[i]\n", "            class_correct[label] += c[i].item()\n", "            class_total[label] += 1"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy of pulsar : 63 %\n", "Accuracy of nonpulsar : 99 %\n"]}], "source": ["for i in range(2):\n", "    print('Accuracy of %5s : %2d %%' % (classes[i], 100 * class_correct[i] / class_total[i]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}