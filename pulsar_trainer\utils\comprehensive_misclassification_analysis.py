"""
全面误分类分析工具，用于CoAtNet脉冲星分类系统。

该模块提供完整的误分类分析功能，包括假阳性和假阴性的详细分析，
生成单个综合报告文件，支持99.7%性能目标的达成和学术研究需求。
"""

import numpy as np
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime
import logging


class ComprehensiveMisclassificationAnalyzer:
    """
    全面的误分类分析器，支持假阳性和假阴性的完整分析。
    生成单个综合报告文件，包含所有误分类样本的详细信息。
    """

    def __init__(self, output_dir: Path, modality: str):
        """
        初始化全面误分类分析器。

        Args:
            output_dir: 输出目录
            modality: 数据模态 (FPP 或 TPP)
        """
        self.output_dir = Path(output_dir)
        self.modality = modality
        self.logger = logging.getLogger(__name__)

        # 创建分析目录
        self.analysis_dir = self.output_dir / 'comprehensive_misclassification_analysis'
        self.analysis_dir.mkdir(parents=True, exist_ok=True)

    def analyze_all_misclassifications(self, dataset, predictions: np.ndarray,
                                     targets: np.ndarray, probabilities: np.ndarray) -> Dict[str, Any]:
        """
        执行全面的误分类分析。

        Args:
            dataset: 数据集对象
            predictions: 模型预测结果
            targets: 真实标签
            probabilities: 预测概率

        Returns:
            完整的分析结果字典
        """
        self.logger.info("开始全面误分类分析...")

        # 收集误分类样本
        false_positives = self._collect_false_positives(dataset, predictions, targets, probabilities)
        false_negatives = self._collect_false_negatives(dataset, predictions, targets, probabilities)

        # 计算统计指标
        stats = self._calculate_comprehensive_stats(predictions, targets, probabilities)

        # 分析置信度模式
        confidence_analysis = self._analyze_confidence_patterns(false_positives, false_negatives,
                                                              predictions, targets, probabilities)

        # 分析样本特征
        feature_analysis = self._analyze_sample_features(false_positives, false_negatives)

        # 组装分析结果
        analysis_results = {
            'metadata': {
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'modality': self.modality,
                'model_config': 'CoAtNet-Large',
                'total_samples': len(targets)
            },
            'statistics': stats,
            'false_positives': false_positives,
            'false_negatives': false_negatives,
            'confidence_analysis': confidence_analysis,
            'feature_analysis': feature_analysis
        }

        # 生成综合报告
        self._generate_comprehensive_report(analysis_results)

        self.logger.info(f"全面误分类分析完成。报告保存至: {self.analysis_dir}")

        return analysis_results

    def _collect_false_positives(self, dataset, predictions, targets, probabilities) -> List[Dict]:
        """收集假阳性样本（非脉冲星被误分类为脉冲星）"""
        false_positives = []

        # 找到假阳性样本的索引
        fp_indices = np.where((targets == 0) & (predictions == 1))[0]

        for idx in fp_indices:
            file_path = str(dataset.file_paths[idx])
            candidate_id = self._extract_candidate_id(file_path)

            sample_info = {
                'index': int(idx),
                'file_path': file_path,
                'file_name': Path(file_path).name,
                'candidate_id': candidate_id,
                'true_label': int(targets[idx]),
                'predicted_label': int(predictions[idx]),
                'prediction_confidence': float(probabilities[idx, predictions[idx]]),
                'pulsar_probability': float(probabilities[idx, 1]),
                'non_pulsar_probability': float(probabilities[idx, 0])
            }
            false_positives.append(sample_info)

        # 按置信度降序排序
        false_positives.sort(key=lambda x: x['prediction_confidence'], reverse=True)

        return false_positives

    def _collect_false_negatives(self, dataset, predictions, targets, probabilities) -> List[Dict]:
        """收集假阴性样本（脉冲星被误分类为非脉冲星）"""
        false_negatives = []

        # 找到假阴性样本的索引
        fn_indices = np.where((targets == 1) & (predictions == 0))[0]

        for idx in fn_indices:
            file_path = str(dataset.file_paths[idx])
            pulsar_id = self._extract_pulsar_id(file_path)

            sample_info = {
                'index': int(idx),
                'file_path': file_path,
                'file_name': Path(file_path).name,
                'pulsar_id': pulsar_id,
                'true_label': int(targets[idx]),
                'predicted_label': int(predictions[idx]),
                'prediction_confidence': float(probabilities[idx, predictions[idx]]),
                'pulsar_probability': float(probabilities[idx, 1]),
                'non_pulsar_probability': float(probabilities[idx, 0])
            }
            false_negatives.append(sample_info)

        # 按置信度降序排序
        false_negatives.sort(key=lambda x: x['prediction_confidence'], reverse=True)

        return false_negatives

    def _extract_candidate_id(self, file_path: str) -> str:
        """从文件路径中提取候选编号"""
        # 匹配 cand_XXXXXX_negative.npy 格式
        match = re.search(r'cand_(\d+)_negative\.npy', file_path)
        return match.group(1) if match else "未知"

    def _extract_pulsar_id(self, file_path: str) -> str:
        """从文件路径中提取脉冲星编号"""
        # 匹配 pulsar_XXX_positive.npy 格式
        match = re.search(r'pulsar_(\d+)_positive\.npy', file_path)
        return match.group(1) if match else "未知"

    def _calculate_comprehensive_stats(self, predictions, targets, probabilities) -> Dict[str, Any]:
        """计算全面的统计指标"""
        # 混淆矩阵元素
        tp = np.sum((targets == 1) & (predictions == 1))
        tn = np.sum((targets == 0) & (predictions == 0))
        fp = np.sum((targets == 0) & (predictions == 1))
        fn = np.sum((targets == 1) & (predictions == 0))

        # 基本指标
        total_samples = len(targets)
        correct_predictions = tp + tn
        total_misclassifications = fp + fn

        accuracy = correct_predictions / total_samples
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

        false_positive_rate = fp / (fp + tn) if (fp + tn) > 0 else 0.0
        false_negative_rate = fn / (fn + tp) if (fn + tp) > 0 else 0.0

        return {
            'total_samples': int(total_samples),
            'true_pulsars': int(np.sum(targets == 1)),
            'true_non_pulsars': int(np.sum(targets == 0)),
            'correct_predictions': int(correct_predictions),
            'total_misclassifications': int(total_misclassifications),
            'confusion_matrix': {
                'tp': int(tp), 'tn': int(tn), 'fp': int(fp), 'fn': int(fn)
            },
            'metrics': {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'false_positive_rate': false_positive_rate,
                'false_negative_rate': false_negative_rate
            }
        }

    def _analyze_confidence_patterns(self, false_positives, false_negatives,
                                   predictions, targets, probabilities) -> Dict[str, Any]:
        """分析置信度模式"""
        # 正确分类的置信度
        correct_mask = (predictions == targets)
        correct_confidences = probabilities[correct_mask, predictions[correct_mask]]

        # 误分类的置信度
        incorrect_mask = (predictions != targets)
        incorrect_confidences = probabilities[incorrect_mask, predictions[incorrect_mask]]

        # 假阳性和假阴性的置信度
        fp_confidences = [sample['prediction_confidence'] for sample in false_positives]
        fn_confidences = [sample['prediction_confidence'] for sample in false_negatives]

        # 置信度分布统计
        def analyze_confidence_distribution(confidences):
            if len(confidences) == 0:
                return {
                    'count': 0, 'mean': 0, 'std': 0, 'min': 0, 'max': 0,
                    'very_high': 0, 'high': 0, 'medium': 0, 'low': 0
                }

            confidences = np.array(confidences)
            return {
                'count': len(confidences),
                'mean': float(np.mean(confidences)),
                'std': float(np.std(confidences)),
                'min': float(np.min(confidences)),
                'max': float(np.max(confidences)),
                'very_high': int(np.sum(confidences > 0.9)),
                'high': int(np.sum((confidences > 0.8) & (confidences <= 0.9))),
                'medium': int(np.sum((confidences > 0.6) & (confidences <= 0.8))),
                'low': int(np.sum(confidences <= 0.6))
            }

        return {
            'correct_predictions': analyze_confidence_distribution(correct_confidences),
            'incorrect_predictions': analyze_confidence_distribution(incorrect_confidences),
            'false_positives': analyze_confidence_distribution(fp_confidences),
            'false_negatives': analyze_confidence_distribution(fn_confidences)
        }

    def _analyze_sample_features(self, false_positives, false_negatives) -> Dict[str, Any]:
        """分析样本特征"""
        # 假阳性特征分析
        fp_candidate_ids = [sample['candidate_id'] for sample in false_positives if sample['candidate_id'] != "未知"]
        fp_analysis = {
            'count': len(false_positives),
            'candidate_id_range': f"{min(fp_candidate_ids)} - {max(fp_candidate_ids)}" if fp_candidate_ids else "无",
            'unique_candidates': len(set(fp_candidate_ids)) if fp_candidate_ids else 0
        }

        # 假阴性特征分析
        fn_pulsar_ids = [sample['pulsar_id'] for sample in false_negatives if sample['pulsar_id'] != "未知"]
        fn_analysis = {
            'count': len(false_negatives),
            'pulsar_id_range': f"{min(fn_pulsar_ids)} - {max(fn_pulsar_ids)}" if fn_pulsar_ids else "无",
            'unique_pulsars': len(set(fn_pulsar_ids)) if fn_pulsar_ids else 0
        }

        return {
            'false_positives': fp_analysis,
            'false_negatives': fn_analysis
        }

    def _generate_comprehensive_report(self, analysis_results: Dict[str, Any]) -> None:
        """生成单个综合报告文件"""
        report_file = self.analysis_dir / 'comprehensive_misclassification_report.txt'

        with open(report_file, 'w', encoding='utf-8') as f:
            # 报告头部
            f.write("=" * 80 + "\n")
            f.write("                CoAtNet脉冲星分类系统误分类分析报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"分析时间: {analysis_results['metadata']['analysis_time']}\n")
            f.write(f"数据模态: {analysis_results['metadata']['modality']}\n")
            f.write(f"模型配置: {analysis_results['metadata']['model_config']}\n")
            f.write("=" * 80 + "\n\n")

            # 一、总体统计摘要
            self._write_overall_statistics(f, analysis_results)

            # 二、假阳性分析
            self._write_false_positive_analysis(f, analysis_results)

            # 三、假阴性分析
            self._write_false_negative_analysis(f, analysis_results)

            # 四、置信度分析
            self._write_confidence_analysis(f, analysis_results)

            # 五、样本特征统计
            self._write_feature_analysis(f, analysis_results)

            # 六、技术附录
            self._write_technical_appendix(f, analysis_results)

        self.logger.info(f"综合误分类分析报告已生成: {report_file}")

    def _write_overall_statistics(self, f, analysis_results):
        """写入总体统计摘要"""
        stats = analysis_results['statistics']
        cm = stats['confusion_matrix']
        metrics = stats['metrics']

        f.write("一、总体统计摘要\n")
        f.write("=" * 80 + "\n")
        f.write(f"测试集总样本数: {stats['total_samples']}\n")
        f.write(f"真实脉冲星数量: {stats['true_pulsars']}\n")
        f.write(f"真实非脉冲星数量: {stats['true_non_pulsars']}\n\n")

        f.write("分类结果统计:\n")
        f.write(f"- 正确分类: {stats['correct_predictions']} ({stats['correct_predictions']/stats['total_samples']*100:.2f}%)\n")
        f.write(f"- 误分类总数: {stats['total_misclassifications']} ({stats['total_misclassifications']/stats['total_samples']*100:.2f}%)\n")
        f.write(f"  - 假阳性 (FP): {cm['fp']} ({cm['fp']/stats['total_samples']*100:.2f}%)\n")
        f.write(f"  - 假阴性 (FN): {cm['fn']} ({cm['fn']/stats['total_samples']*100:.2f}%)\n\n")

        f.write("性能指标:\n")
        f.write(f"- 准确率 (Accuracy): {metrics['accuracy']*100:.2f}%\n")
        f.write(f"- 精确率 (Precision): {metrics['precision']*100:.2f}%\n")
        f.write(f"- 召回率 (Recall): {metrics['recall']*100:.2f}%\n")
        f.write(f"- F1分数: {metrics['f1_score']*100:.2f}%\n")
        f.write(f"- 假阳性率 (FPR): {metrics['false_positive_rate']*100:.2f}%\n")
        f.write(f"- 假阴性率 (FNR): {metrics['false_negative_rate']*100:.2f}%\n\n")

        f.write("混淆矩阵:\n")
        f.write("                预测\n")
        f.write("实际    非脉冲星  脉冲星\n")
        f.write(f"非脉冲星   {cm['tn']:4d}    {cm['fp']:4d}\n")
        f.write(f"脉冲星     {cm['fn']:4d}    {cm['tp']:4d}\n\n")
        f.write("=" * 80 + "\n\n")

    def _write_false_positive_analysis(self, f, analysis_results):
        """写入假阳性分析"""
        false_positives = analysis_results['false_positives']
        fp_conf = analysis_results['confidence_analysis']['false_positives']

        f.write("二、假阳性分析（非脉冲星被误分类为脉冲星）\n")
        f.write("=" * 80 + "\n")
        f.write(f"假阳性样本总数: {len(false_positives)}\n")
        f.write(f"假阳性率: {analysis_results['statistics']['metrics']['false_positive_rate']*100:.2f}%\n\n")

        if len(false_positives) > 0:
            f.write("置信度统计:\n")
            f.write(f"- 平均置信度: {fp_conf['mean']:.3f}\n")
            f.write(f"- 置信度标准差: {fp_conf['std']:.3f}\n")
            f.write(f"- 最高置信度: {fp_conf['max']:.3f}\n")
            f.write(f"- 最低置信度: {fp_conf['min']:.3f}\n\n")

            f.write("置信度分布:\n")
            f.write(f"- 极高置信度 (>0.9): {fp_conf['very_high']} 个\n")
            f.write(f"- 高置信度 (0.8-0.9): {fp_conf['high']} 个\n")
            f.write(f"- 中等置信度 (0.6-0.8): {fp_conf['medium']} 个\n")
            f.write(f"- 低置信度 (≤0.6): {fp_conf['low']} 个\n\n")

            f.write("详细样本列表:\n")
            f.write("[按置信度降序排列]\n\n")

            for i, sample in enumerate(false_positives, 1):
                f.write(f"{i}. 文件: {sample['file_name']}\n")
                f.write(f"   测试集索引: {sample['index']}\n")
                f.write(f"   真实标签: {sample['true_label']} (非脉冲星)\n")
                f.write(f"   预测标签: {sample['predicted_label']} (脉冲星)\n")
                f.write(f"   预测置信度: {sample['prediction_confidence']:.3f}\n")
                f.write(f"   脉冲星概率: {sample['pulsar_probability']:.3f}\n")
                f.write(f"   候选编号: {sample['candidate_id']}\n\n")
        else:
            f.write("🎉 未发现假阳性样本！模型在避免将非脉冲星误分类为脉冲星方面表现完美。\n\n")

        f.write("=" * 80 + "\n\n")

    def _write_false_negative_analysis(self, f, analysis_results):
        """写入假阴性分析"""
        false_negatives = analysis_results['false_negatives']
        fn_conf = analysis_results['confidence_analysis']['false_negatives']

        f.write("三、假阴性分析（脉冲星被误分类为非脉冲星）\n")
        f.write("=" * 80 + "\n")
        f.write(f"假阴性样本总数: {len(false_negatives)}\n")
        f.write(f"假阴性率: {analysis_results['statistics']['metrics']['false_negative_rate']*100:.2f}%\n\n")

        if len(false_negatives) > 0:
            f.write("置信度统计:\n")
            f.write(f"- 平均置信度: {fn_conf['mean']:.3f}\n")
            f.write(f"- 置信度标准差: {fn_conf['std']:.3f}\n")
            f.write(f"- 最高置信度: {fn_conf['max']:.3f}\n")
            f.write(f"- 最低置信度: {fn_conf['min']:.3f}\n\n")

            f.write("置信度分布:\n")
            f.write(f"- 极高置信度 (>0.9): {fn_conf['very_high']} 个\n")
            f.write(f"- 高置信度 (0.8-0.9): {fn_conf['high']} 个\n")
            f.write(f"- 中等置信度 (0.6-0.8): {fn_conf['medium']} 个\n")
            f.write(f"- 低置信度 (≤0.6): {fn_conf['low']} 个\n\n")

            f.write("详细样本列表:\n")
            f.write("[按置信度降序排列]\n\n")

            for i, sample in enumerate(false_negatives, 1):
                f.write(f"{i}. 文件: {sample['file_name']}\n")
                f.write(f"   测试集索引: {sample['index']}\n")
                f.write(f"   真实标签: {sample['true_label']} (脉冲星)\n")
                f.write(f"   预测标签: {sample['predicted_label']} (非脉冲星)\n")
                f.write(f"   预测置信度: {sample['prediction_confidence']:.3f}\n")
                f.write(f"   脉冲星概率: {sample['pulsar_probability']:.3f}\n")
                f.write(f"   脉冲星编号: {sample['pulsar_id']}\n\n")
        else:
            f.write("🎉 未发现假阴性样本！模型在检测所有脉冲星方面表现完美。\n\n")

        f.write("=" * 80 + "\n\n")

    def _write_confidence_analysis(self, f, analysis_results):
        """写入置信度分析"""
        conf_analysis = analysis_results['confidence_analysis']

        f.write("四、置信度分析\n")
        f.write("=" * 80 + "\n")

        f.write("整体置信度分布:\n")
        f.write(f"- 正确分类平均置信度: {conf_analysis['correct_predictions']['mean']:.3f}\n")
        f.write(f"- 误分类平均置信度: {conf_analysis['incorrect_predictions']['mean']:.3f}\n\n")

        f.write("高置信度错误分析:\n")
        fp_high = conf_analysis['false_positives']['very_high'] + conf_analysis['false_positives']['high']
        fn_high = conf_analysis['false_negatives']['very_high'] + conf_analysis['false_negatives']['high']
        f.write(f"- 高置信度假阳性 (>0.8): {fp_high} 个\n")
        f.write(f"- 高置信度假阴性 (>0.8): {fn_high} 个\n\n")

        f.write("=" * 80 + "\n\n")

    def _write_feature_analysis(self, f, analysis_results):
        """写入样本特征统计"""
        feature_analysis = analysis_results['feature_analysis']

        f.write("五、样本特征统计\n")
        f.write("=" * 80 + "\n")

        f.write("假阳性样本特征:\n")
        fp_features = feature_analysis['false_positives']
        f.write(f"- 样本数量: {fp_features['count']}\n")
        f.write(f"- 候选编号范围: {fp_features['candidate_id_range']}\n")
        f.write(f"- 唯一候选数: {fp_features['unique_candidates']}\n\n")

        f.write("假阴性样本特征:\n")
        fn_features = feature_analysis['false_negatives']
        f.write(f"- 样本数量: {fn_features['count']}\n")
        f.write(f"- 脉冲星编号范围: {fn_features['pulsar_id_range']}\n")
        f.write(f"- 唯一脉冲星数: {fn_features['unique_pulsars']}\n\n")

        f.write("=" * 80 + "\n\n")

    def _write_technical_appendix(self, f, analysis_results):
        """写入技术附录"""
        f.write("六、技术附录\n")
        f.write("=" * 80 + "\n")

        f.write("分析参数:\n")
        f.write("- 置信度阈值: 0.5\n")
        f.write("- 分析样本数: 全部误分类样本\n")
        f.write("- 特征提取方法: 文件名解析和统计分析\n\n")

        f.write("文件命名规范:\n")
        f.write("- 假阳性: cand_[候选编号]_negative.npy\n")
        f.write("- 假阴性: pulsar_[脉冲星编号]_positive.npy\n\n")

        f.write(f"报告生成时间: {analysis_results['metadata']['analysis_time']}\n\n")

        f.write("=" * 80 + "\n")
        f.write("                                报告结束\n")
        f.write("=" * 80 + "\n")


def create_comprehensive_misclassification_analyzer(output_dir: Path, modality: str) -> ComprehensiveMisclassificationAnalyzer:
    """
    创建全面误分类分析器实例。

    Args:
        output_dir: 输出目录
        modality: 数据模态

    Returns:
        ComprehensiveMisclassificationAnalyzer实例
    """
    return ComprehensiveMisclassificationAnalyzer(output_dir, modality)