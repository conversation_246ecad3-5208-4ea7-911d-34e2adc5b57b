"""
Visualization utilities for GFNet pulsar classification.

This module provides functions to create professional academic-quality
visualizations for training results and evaluation metrics.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import logging
from sklearn.metrics import roc_curve, auc, precision_recall_curve


# Set matplotlib style for academic publications
plt.style.use('default')
sns.set_palette("husl")

# Configure matplotlib for better quality
plt.rcParams.update({
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16,
    'font.family': 'serif',
    'font.serif': ['Times New Roman', 'DejaVu Serif'],
    'mathtext.fontset': 'stix',
    'axes.grid': True,
    'grid.alpha': 0.3
})


class PulsarVisualizer:
    """Professional visualizer for pulsar classification results."""
    
    def __init__(self, output_dir: Path, modality: str = 'FPP'):
        """
        Initialize visualizer.
        
        Args:
            output_dir: Directory to save plots
            modality: Data modality (FPP or TPP)
        """
        self.output_dir = Path(output_dir)
        self.modality = modality
        self.logger = logging.getLogger(__name__)
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def plot_training_curves(self, training_history: Dict[str, List[float]]) -> None:
        """
        Plot training and validation curves.
        
        Args:
            training_history: Dictionary containing training history
        """
        epochs = range(1, len(training_history['train_loss']) + 1)
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'GFNet Training Progress - {self.modality} Modality', fontsize=16, fontweight='bold')
        
        # Plot 1: Training and Validation Loss
        ax1.plot(epochs, training_history['train_loss'], 'b-', label='Training Loss', linewidth=2)
        ax1.plot(epochs, training_history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
        ax1.set_title('Loss Curves')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Training and Validation Accuracy
        ax2.plot(epochs, training_history['train_acc'], 'b-', label='Training Accuracy', linewidth=2)
        ax2.plot(epochs, training_history['val_acc'], 'r-', label='Validation Accuracy', linewidth=2)
        ax2.set_title('Accuracy Curves')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Learning Rate Schedule
        if 'learning_rates' in training_history:
            ax3.plot(epochs, training_history['learning_rates'], 'g-', linewidth=2)
            ax3.set_title('Learning Rate Schedule')
            ax3.set_xlabel('Epoch')
            ax3.set_ylabel('Learning Rate')
            ax3.set_yscale('log')
            ax3.grid(True, alpha=0.3)
        else:
            ax3.text(0.5, 0.5, 'Learning Rate\nData Not Available', 
                    ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('Learning Rate Schedule')
        
        # Plot 4: Loss Difference (Overfitting indicator)
        loss_diff = np.array(training_history['val_loss']) - np.array(training_history['train_loss'])
        ax4.plot(epochs, loss_diff, 'purple', linewidth=2)
        ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax4.set_title('Validation - Training Loss')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Loss Difference')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        save_path = self.output_dir / f'training_curves_{self.modality.lower()}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"Training curves saved to: {save_path}")
    
    def plot_confusion_matrix(self, confusion_matrix: Dict[str, int], 
                            class_names: List[str] = None) -> None:
        """
        Plot confusion matrix heatmap.
        
        Args:
            confusion_matrix: Dictionary with tp, tn, fp, fn values
            class_names: Names of classes
        """
        if class_names is None:
            class_names = ['Non-Pulsar', 'Pulsar']
        
        # Create confusion matrix array
        cm_array = np.array([
            [confusion_matrix['tn'], confusion_matrix['fp']],
            [confusion_matrix['fn'], confusion_matrix['tp']]
        ])
        
        # Calculate percentages
        cm_percent = cm_array.astype('float') / cm_array.sum() * 100
        
        # Create figure
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # Create heatmap
        sns.heatmap(cm_array, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=class_names, yticklabels=class_names,
                   ax=ax, cbar_kws={'label': 'Count'})
        
        # Add percentage annotations
        for i in range(2):
            for j in range(2):
                ax.text(j + 0.5, i + 0.7, f'({cm_percent[i, j]:.1f}%)', 
                       ha='center', va='center', fontsize=10, color='gray')
        
        ax.set_title(f'Confusion Matrix - {self.modality} Modality', fontsize=14, fontweight='bold')
        ax.set_xlabel('Predicted Label')
        ax.set_ylabel('True Label')
        
        plt.tight_layout()
        
        # Save plot
        save_path = self.output_dir / f'confusion_matrix_{self.modality.lower()}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"Confusion matrix saved to: {save_path}")
    
    def plot_roc_curve(self, targets: np.ndarray, probabilities: np.ndarray) -> None:
        """
        Plot ROC curve.
        
        Args:
            targets: True labels
            probabilities: Predicted probabilities
        """
        # Calculate ROC curve
        fpr, tpr, _ = roc_curve(targets, probabilities[:, 1])
        roc_auc = auc(fpr, tpr)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # Plot ROC curve
        ax.plot(fpr, tpr, color='darkorange', lw=2, 
               label=f'ROC Curve (AUC = {roc_auc:.3f})')
        ax.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', alpha=0.5)
        
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('False Positive Rate')
        ax.set_ylabel('True Positive Rate')
        ax.set_title(f'ROC Curve - {self.modality} Modality', fontsize=14, fontweight='bold')
        ax.legend(loc="lower right")
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        save_path = self.output_dir / f'roc_curve_{self.modality.lower()}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"ROC curve saved to: {save_path}")
    
    def plot_precision_recall_curve(self, targets: np.ndarray, probabilities: np.ndarray) -> None:
        """
        Plot Precision-Recall curve.
        
        Args:
            targets: True labels
            probabilities: Predicted probabilities
        """
        # Calculate PR curve
        precision, recall, _ = precision_recall_curve(targets, probabilities[:, 1])
        pr_auc = auc(recall, precision)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # Plot PR curve
        ax.plot(recall, precision, color='blue', lw=2, 
               label=f'PR Curve (AUC = {pr_auc:.3f})')
        
        # Add baseline (random classifier)
        baseline = np.sum(targets) / len(targets)
        ax.axhline(y=baseline, color='red', linestyle='--', alpha=0.5, 
                  label=f'Baseline (Random) = {baseline:.3f}')
        
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('Recall')
        ax.set_ylabel('Precision')
        ax.set_title(f'Precision-Recall Curve - {self.modality} Modality', fontsize=14, fontweight='bold')
        ax.legend(loc="lower left")
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        save_path = self.output_dir / f'pr_curve_{self.modality.lower()}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"PR curve saved to: {save_path}")
    
    def plot_metrics_summary(self, metrics: Dict[str, float]) -> None:
        """
        Plot metrics summary bar chart.
        
        Args:
            metrics: Dictionary of metric names and values
        """
        # Filter metrics for visualization
        viz_metrics = {
            'Accuracy': metrics.get('accuracy', 0),
            'Precision': metrics.get('precision', 0),
            'Recall': metrics.get('recall', 0),
            'F1-Score': metrics.get('f1_score', 0),
            'Specificity': metrics.get('specificity', 0)
        }
        
        # Create figure
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create bar chart
        metric_names = list(viz_metrics.keys())
        metric_values = list(viz_metrics.values())
        
        bars = ax.bar(metric_names, metric_values, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
        
        # Add value labels on bars
        for bar, value in zip(bars, metric_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_ylim([0, 1.1])
        ax.set_ylabel('Score')
        ax.set_title(f'Performance Metrics Summary - {self.modality} Modality', 
                    fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3, axis='y')
        
        # Rotate x-axis labels if needed
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        # Save plot
        save_path = self.output_dir / f'metrics_summary_{self.modality.lower()}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"Metrics summary saved to: {save_path}")
    
    def create_all_plots(self, training_history: Dict[str, List[float]], 
                        eval_results: Dict[str, Any]) -> None:
        """
        Create all visualization plots.
        
        Args:
            training_history: Training history data
            eval_results: Evaluation results
        """
        self.logger.info(f"Creating visualization plots for {self.modality} modality...")
        
        # Plot training curves
        self.plot_training_curves(training_history)
        
        # Plot confusion matrix
        if 'confusion_matrix' in eval_results:
            self.plot_confusion_matrix(eval_results['confusion_matrix'])
        
        # Plot ROC and PR curves
        if 'targets' in eval_results and 'probabilities' in eval_results:
            self.plot_roc_curve(eval_results['targets'], eval_results['probabilities'])
            self.plot_precision_recall_curve(eval_results['targets'], eval_results['probabilities'])
        
        # Plot metrics summary
        if 'basic_metrics' in eval_results:
            self.plot_metrics_summary(eval_results['basic_metrics'])
        
        self.logger.info(f"All visualization plots created in: {self.output_dir}")


def create_visualizer(output_dir: Path, modality: str) -> PulsarVisualizer:
    """
    Create a visualizer instance.
    
    Args:
        output_dir: Output directory for plots
        modality: Data modality
        
    Returns:
        PulsarVisualizer instance
    """
    return PulsarVisualizer(output_dir, modality)
